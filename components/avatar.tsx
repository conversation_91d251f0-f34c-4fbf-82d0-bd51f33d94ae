"use client";

import * as React from "react";
import { DropdownMenuItem } from "@radix-ui/react-dropdown-menu";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar } from "./ui/avatar";
import { AvatarFallback, AvatarImage } from "@radix-ui/react-avatar";
import { signOut, useSession } from "@/lib/auth-client";
import Link from "next/link";

export function AvatarMenu() {
  const session = useSession();
  if (!session) {
    return (
      <Button variant={"outline"}>
        <Link href="/sign-in">Sign in</Link>
      </Button>
    );
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Avatar>
            <AvatarImage src={session?.data?.user?.image} />
            <AvatarFallback>{session?.data?.user?.name}</AvatarFallback>
          </Avatar>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="mt-2 w-32">
          <DropdownMenuLabel>{session?.data?.user?.name}</DropdownMenuLabel>
          <DropdownMenuItem>
            <Button variant={"outline"} onClick={() => signOut()}>
              Sign out
            </Button>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
}

"use client";
import Link from "next/link";
import Image from "next/image";
import { useSession } from "@/lib/auth-client";

export const Navbar = () => {
  const session = useSession();
  return (
    <nav className="container mt-5 flex items-center justify-between">
      <Link href={"/"}>Home</Link>
      <div>
        {session ? (
          <Link href={"/profile"}>
            <Image
              src={session?.data?.user?.image || "/globe.svg"}
              alt={session?.data?.user?.name || "placeholder"}
              width={50}
              height={50}
              className="rounded-full"
            />
          </Link>
        ) : (
          <Link href={"/sign-in"}>Sign In</Link>
        )}
      </div>
    </nav>
  );
};

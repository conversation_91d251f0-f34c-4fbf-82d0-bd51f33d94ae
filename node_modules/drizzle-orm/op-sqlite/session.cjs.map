{"version": 3, "sources": ["../../src/op-sqlite/session.ts"], "sourcesContent": ["import type { OPSQLiteConnection, QueryResult } from '@op-engineering/op-sqlite';\nimport { type Cache, NoopCache } from '~/cache/core/index.ts';\nimport type { WithCacheConfig } from '~/cache/core/types.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { NoopLogger } from '~/logger.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { fillPlaceholders, type Query, sql } from '~/sql/sql.ts';\nimport type { SQLiteAsyncDialect } from '~/sqlite-core/dialect.ts';\nimport { SQLiteTransaction } from '~/sqlite-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/sqlite-core/query-builders/select.types.ts';\nimport {\n\ttype PreparedQueryConfig as PreparedQueryConfigBase,\n\ttype SQLiteExecuteMethod,\n\tSQLitePreparedQuery,\n\tSQLiteSession,\n\ttype SQLiteTransactionConfig,\n} from '~/sqlite-core/session.ts';\nimport { mapResultRow } from '~/utils.ts';\n\nexport interface OPSQLiteSessionOptions {\n\tlogger?: Logger;\n\tcache?: Cache;\n}\n\ntype PreparedQueryConfig = Omit<PreparedQueryConfigBase, 'statement' | 'run'>;\n\nexport class OPSQLiteSession<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends SQLiteSession<'async', QueryResult, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'OPSQLiteSession';\n\n\tprivate logger: Logger;\n\tprivate cache: Cache;\n\n\tconstructor(\n\t\tprivate client: OPSQLiteConnection,\n\t\tdialect: SQLiteAsyncDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\toptions: OPSQLiteSessionOptions = {},\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t\tthis.cache = options.cache ?? new NoopCache();\n\t}\n\n\tprepareQuery<T extends Omit<PreparedQueryConfig, 'run'>>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\texecuteMethod: SQLiteExecuteMethod,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][]) => unknown,\n\t\tqueryMetadata?: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t},\n\t\tcacheConfig?: WithCacheConfig,\n\t): OPSQLitePreparedQuery<T> {\n\t\treturn new OPSQLitePreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery,\n\t\t\tthis.logger,\n\t\t\tthis.cache,\n\t\t\tqueryMetadata,\n\t\t\tcacheConfig,\n\t\t\tfields,\n\t\t\texecuteMethod,\n\t\t\tisResponseInArrayMode,\n\t\t\tcustomResultMapper,\n\t\t);\n\t}\n\n\toverride transaction<T>(\n\t\ttransaction: (tx: OPSQLiteTransaction<TFullSchema, TSchema>) => T,\n\t\tconfig: SQLiteTransactionConfig = {},\n\t): T {\n\t\tconst tx = new OPSQLiteTransaction('async', this.dialect, this, this.schema);\n\t\tthis.run(sql.raw(`begin${config?.behavior ? ' ' + config.behavior : ''}`));\n\t\ttry {\n\t\t\tconst result = transaction(tx);\n\t\t\tthis.run(sql`commit`);\n\t\t\treturn result;\n\t\t} catch (err) {\n\t\t\tthis.run(sql`rollback`);\n\t\t\tthrow err;\n\t\t}\n\t}\n}\n\nexport class OPSQLiteTransaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends SQLiteTransaction<'async', QueryResult, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'OPSQLiteTransaction';\n\n\toverride transaction<T>(transaction: (tx: OPSQLiteTransaction<TFullSchema, TSchema>) => T): T {\n\t\tconst savepointName = `sp${this.nestedIndex}`;\n\t\tconst tx = new OPSQLiteTransaction('async', this.dialect, this.session, this.schema, this.nestedIndex + 1);\n\t\tthis.session.run(sql.raw(`savepoint ${savepointName}`));\n\t\ttry {\n\t\t\tconst result = transaction(tx);\n\t\t\tthis.session.run(sql.raw(`release savepoint ${savepointName}`));\n\t\t\treturn result;\n\t\t} catch (err) {\n\t\t\tthis.session.run(sql.raw(`rollback to savepoint ${savepointName}`));\n\t\t\tthrow err;\n\t\t}\n\t}\n}\n\nexport class OPSQLitePreparedQuery<T extends PreparedQueryConfig = PreparedQueryConfig> extends SQLitePreparedQuery<\n\t{ type: 'async'; run: QueryResult; all: T['all']; get: T['get']; values: T['values']; execute: T['execute'] }\n> {\n\tstatic override readonly [entityKind]: string = 'OPSQLitePreparedQuery';\n\n\tconstructor(\n\t\tprivate client: OPSQLiteConnection,\n\t\tquery: Query,\n\t\tprivate logger: Logger,\n\t\tcache: Cache,\n\t\tqueryMetadata: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t} | undefined,\n\t\tcacheConfig: WithCacheConfig | undefined,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\texecuteMethod: SQLiteExecuteMethod,\n\t\tprivate _isResponseInArrayMode: boolean,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => unknown,\n\t) {\n\t\tsuper('sync', executeMethod, query, cache, queryMetadata, cacheConfig);\n\t}\n\n\tasync run(placeholderValues?: Record<string, unknown>): Promise<QueryResult> {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n\t\tthis.logger.logQuery(this.query.sql, params);\n\n\t\treturn await this.queryWithCache(this.query.sql, params, async () => {\n\t\t\treturn this.client.executeAsync(this.query.sql, params);\n\t\t});\n\t}\n\n\tasync all(placeholderValues?: Record<string, unknown>): Promise<T['all']> {\n\t\tconst { fields, joinsNotNullableMap, query, logger, customResultMapper, client } = this;\n\t\tif (!fields && !customResultMapper) {\n\t\t\tconst params = fillPlaceholders(query.params, placeholderValues ?? {});\n\t\t\tlogger.logQuery(query.sql, params);\n\n\t\t\treturn await this.queryWithCache(query.sql, params, async () => {\n\t\t\t\treturn client.execute(query.sql, params).rows?._array || [];\n\t\t\t});\n\t\t}\n\n\t\tconst rows = await this.values(placeholderValues) as unknown[][];\n\t\tif (customResultMapper) {\n\t\t\treturn customResultMapper(rows) as T['all'];\n\t\t}\n\t\treturn rows.map((row) => mapResultRow(fields!, row, joinsNotNullableMap));\n\t}\n\n\tasync get(placeholderValues?: Record<string, unknown>): Promise<T['get']> {\n\t\tconst { fields, joinsNotNullableMap, customResultMapper, query, logger, client } = this;\n\t\tconst params = fillPlaceholders(query.params, placeholderValues ?? {});\n\t\tlogger.logQuery(query.sql, params);\n\t\tif (!fields && !customResultMapper) {\n\t\t\tconst rows = await this.queryWithCache(query.sql, params, async () => {\n\t\t\t\treturn client.execute(query.sql, params).rows?._array || [];\n\t\t\t});\n\t\t\treturn rows[0];\n\t\t}\n\n\t\tconst rows = await this.values(placeholderValues) as unknown[][];\n\t\tconst row = rows[0];\n\n\t\tif (!row) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tif (customResultMapper) {\n\t\t\treturn customResultMapper(rows) as T['get'];\n\t\t}\n\n\t\treturn mapResultRow(fields!, row, joinsNotNullableMap);\n\t}\n\n\tasync values(placeholderValues?: Record<string, unknown>): Promise<T['values']> {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n\t\tthis.logger.logQuery(this.query.sql, params);\n\t\treturn await this.queryWithCache(this.query.sql, params, async () => {\n\t\t\treturn await this.client.executeRawAsync(this.query.sql, params);\n\t\t});\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode(): boolean {\n\t\treturn this._isResponseInArrayMode;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,kBAAsC;AAEtC,oBAA2B;AAE3B,oBAA2B;AAE3B,iBAAkD;AAElD,yBAAkC;AAElC,qBAMO;AACP,mBAA6B;AAStB,MAAM,wBAGH,6BAA0D;AAAA,EAMnE,YACS,QACR,SACQ,QACR,UAAkC,CAAC,GAClC;AACD,UAAM,OAAO;AALL;AAEA;AAIR,SAAK,SAAS,QAAQ,UAAU,IAAI,yBAAW;AAC/C,SAAK,QAAQ,QAAQ,SAAS,IAAI,sBAAU;AAAA,EAC7C;AAAA,EAdA,QAA0B,wBAAU,IAAY;AAAA,EAExC;AAAA,EACA;AAAA,EAaR,aACC,OACA,QACA,eACA,uBACA,oBACA,eAIA,aAC2B;AAC3B,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAES,YACR,aACA,SAAkC,CAAC,GAC/B;AACJ,UAAM,KAAK,IAAI,oBAAoB,SAAS,KAAK,SAAS,MAAM,KAAK,MAAM;AAC3E,SAAK,IAAI,eAAI,IAAI,QAAQ,QAAQ,WAAW,MAAM,OAAO,WAAW,EAAE,EAAE,CAAC;AACzE,QAAI;AACH,YAAM,SAAS,YAAY,EAAE;AAC7B,WAAK,IAAI,sBAAW;AACpB,aAAO;AAAA,IACR,SAAS,KAAK;AACb,WAAK,IAAI,wBAAa;AACtB,YAAM;AAAA,IACP;AAAA,EACD;AACD;AAEO,MAAM,4BAGH,qCAA8D;AAAA,EACvE,QAA0B,wBAAU,IAAY;AAAA,EAEvC,YAAe,aAAsE;AAC7F,UAAM,gBAAgB,KAAK,KAAK,WAAW;AAC3C,UAAM,KAAK,IAAI,oBAAoB,SAAS,KAAK,SAAS,KAAK,SAAS,KAAK,QAAQ,KAAK,cAAc,CAAC;AACzG,SAAK,QAAQ,IAAI,eAAI,IAAI,aAAa,aAAa,EAAE,CAAC;AACtD,QAAI;AACH,YAAM,SAAS,YAAY,EAAE;AAC7B,WAAK,QAAQ,IAAI,eAAI,IAAI,qBAAqB,aAAa,EAAE,CAAC;AAC9D,aAAO;AAAA,IACR,SAAS,KAAK;AACb,WAAK,QAAQ,IAAI,eAAI,IAAI,yBAAyB,aAAa,EAAE,CAAC;AAClE,YAAM;AAAA,IACP;AAAA,EACD;AACD;AAEO,MAAM,8BAAmF,mCAE9F;AAAA,EAGD,YACS,QACR,OACQ,QACR,OACA,eAIA,aACQ,QACR,eACQ,wBACA,oBACP;AACD,UAAM,QAAQ,eAAe,OAAO,OAAO,eAAe,WAAW;AAd7D;AAEA;AAOA;AAEA;AACA;AAAA,EAGT;AAAA,EAlBA,QAA0B,wBAAU,IAAY;AAAA,EAoBhD,MAAM,IAAI,mBAAmE;AAC5E,UAAM,aAAS,6BAAiB,KAAK,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AAC1E,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAE3C,WAAO,MAAM,KAAK,eAAe,KAAK,MAAM,KAAK,QAAQ,YAAY;AACpE,aAAO,KAAK,OAAO,aAAa,KAAK,MAAM,KAAK,MAAM;AAAA,IACvD,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,IAAI,mBAAgE;AACzE,UAAM,EAAE,QAAQ,qBAAqB,OAAO,QAAQ,oBAAoB,OAAO,IAAI;AACnF,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,YAAM,aAAS,6BAAiB,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AACrE,aAAO,SAAS,MAAM,KAAK,MAAM;AAEjC,aAAO,MAAM,KAAK,eAAe,MAAM,KAAK,QAAQ,YAAY;AAC/D,eAAO,OAAO,QAAQ,MAAM,KAAK,MAAM,EAAE,MAAM,UAAU,CAAC;AAAA,MAC3D,CAAC;AAAA,IACF;AAEA,UAAM,OAAO,MAAM,KAAK,OAAO,iBAAiB;AAChD,QAAI,oBAAoB;AACvB,aAAO,mBAAmB,IAAI;AAAA,IAC/B;AACA,WAAO,KAAK,IAAI,CAAC,YAAQ,2BAAa,QAAS,KAAK,mBAAmB,CAAC;AAAA,EACzE;AAAA,EAEA,MAAM,IAAI,mBAAgE;AACzE,UAAM,EAAE,QAAQ,qBAAqB,oBAAoB,OAAO,QAAQ,OAAO,IAAI;AACnF,UAAM,aAAS,6BAAiB,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AACrE,WAAO,SAAS,MAAM,KAAK,MAAM;AACjC,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,YAAMA,QAAO,MAAM,KAAK,eAAe,MAAM,KAAK,QAAQ,YAAY;AACrE,eAAO,OAAO,QAAQ,MAAM,KAAK,MAAM,EAAE,MAAM,UAAU,CAAC;AAAA,MAC3D,CAAC;AACD,aAAOA,MAAK,CAAC;AAAA,IACd;AAEA,UAAM,OAAO,MAAM,KAAK,OAAO,iBAAiB;AAChD,UAAM,MAAM,KAAK,CAAC;AAElB,QAAI,CAAC,KAAK;AACT,aAAO;AAAA,IACR;AAEA,QAAI,oBAAoB;AACvB,aAAO,mBAAmB,IAAI;AAAA,IAC/B;AAEA,eAAO,2BAAa,QAAS,KAAK,mBAAmB;AAAA,EACtD;AAAA,EAEA,MAAM,OAAO,mBAAmE;AAC/E,UAAM,aAAS,6BAAiB,KAAK,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AAC1E,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAC3C,WAAO,MAAM,KAAK,eAAe,KAAK,MAAM,KAAK,QAAQ,YAAY;AACpE,aAAO,MAAM,KAAK,OAAO,gBAAgB,KAAK,MAAM,KAAK,MAAM;AAAA,IAChE,CAAC;AAAA,EACF;AAAA;AAAA,EAGA,wBAAiC;AAChC,WAAO,KAAK;AAAA,EACb;AACD;", "names": ["rows"]}
{"version": 3, "sources": ["../../src/pg-proxy/session.ts"], "sourcesContent": ["import { type Cache, NoopCache } from '~/cache/core/cache.ts';\nimport type { WithCacheConfig } from '~/cache/core/types.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { NoopLogger } from '~/logger.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport { PgTransaction } from '~/pg-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/pg-core/query-builders/select.types.ts';\nimport type { PgQueryResultHKT, PgTransactionConfig, PreparedQueryConfig } from '~/pg-core/session.ts';\nimport { PgPreparedQuery as PreparedQueryBase, PgSession } from '~/pg-core/session.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport type { QueryWithTypings } from '~/sql/sql.ts';\nimport { fillPlaceholders } from '~/sql/sql.ts';\nimport { tracer } from '~/tracing.ts';\nimport { type Assume, mapResultRow } from '~/utils.ts';\nimport type { RemoteCallback } from './driver.ts';\n\nexport interface PgRemoteSessionOptions {\n\tlogger?: Logger;\n\tcache?: Cache;\n}\n\nexport class PgRemoteSession<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgSession<PgRemoteQueryResultHKT, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'PgRemoteSession';\n\n\tprivate logger: Logger;\n\tprivate cache: Cache;\n\n\tconstructor(\n\t\tprivate client: RemoteCallback,\n\t\tdialect: PgDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\toptions: PgRemoteSessionOptions = {},\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t\tthis.cache = options.cache ?? new NoopCache();\n\t}\n\n\tprepareQuery<T extends PreparedQueryConfig>(\n\t\tquery: QueryWithTypings,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][]) => T['execute'],\n\t\tqueryMetadata?: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t},\n\t\tcacheConfig?: WithCacheConfig,\n\t): PreparedQuery<T> {\n\t\treturn new PreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery.sql,\n\t\t\tquery.params,\n\t\t\tquery.typings,\n\t\t\tthis.logger,\n\t\t\tthis.cache,\n\t\t\tqueryMetadata,\n\t\t\tcacheConfig,\n\t\t\tfields,\n\t\t\tisResponseInArrayMode,\n\t\t\tcustomResultMapper,\n\t\t);\n\t}\n\n\toverride async transaction<T>(\n\t\t_transaction: (tx: PgProxyTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t\t_config?: PgTransactionConfig,\n\t): Promise<T> {\n\t\tthrow new Error('Transactions are not supported by the Postgres Proxy driver');\n\t}\n}\n\nexport class PgProxyTransaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgTransaction<PgRemoteQueryResultHKT, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'PgProxyTransaction';\n\n\toverride async transaction<T>(\n\t\t_transaction: (tx: PgProxyTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t): Promise<T> {\n\t\tthrow new Error('Transactions are not supported by the Postgres Proxy driver');\n\t}\n}\n\nexport class PreparedQuery<T extends PreparedQueryConfig> extends PreparedQueryBase<T> {\n\tstatic override readonly [entityKind]: string = 'PgProxyPreparedQuery';\n\n\tconstructor(\n\t\tprivate client: RemoteCallback,\n\t\tprivate queryString: string,\n\t\tprivate params: unknown[],\n\t\tprivate typings: any[] | undefined,\n\t\tprivate logger: Logger,\n\t\tcache: Cache,\n\t\tqueryMetadata: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t} | undefined,\n\t\tcacheConfig: WithCacheConfig | undefined,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\tprivate _isResponseInArrayMode: boolean,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => T['execute'],\n\t) {\n\t\tsuper({ sql: queryString, params }, cache, queryMetadata, cacheConfig);\n\t}\n\n\tasync execute(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['execute']> {\n\t\treturn tracer.startActiveSpan('drizzle.execute', async (span) => {\n\t\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\t\t\tconst { fields, client, queryString, joinsNotNullableMap, customResultMapper, logger, typings } = this;\n\n\t\t\tspan?.setAttributes({\n\t\t\t\t'drizzle.query.text': queryString,\n\t\t\t\t'drizzle.query.params': JSON.stringify(params),\n\t\t\t});\n\n\t\t\tlogger.logQuery(queryString, params);\n\n\t\t\tif (!fields && !customResultMapper) {\n\t\t\t\treturn tracer.startActiveSpan('drizzle.driver.execute', async () => {\n\t\t\t\t\tconst { rows } = await this.queryWithCache(queryString, params, async () => {\n\t\t\t\t\t\treturn await client(queryString, params as any[], 'execute', typings);\n\t\t\t\t\t});\n\n\t\t\t\t\treturn rows;\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tconst rows = await tracer.startActiveSpan('drizzle.driver.execute', async () => {\n\t\t\t\tspan?.setAttributes({\n\t\t\t\t\t'drizzle.query.text': queryString,\n\t\t\t\t\t'drizzle.query.params': JSON.stringify(params),\n\t\t\t\t});\n\n\t\t\t\tconst { rows } = await this.queryWithCache(queryString, params, async () => {\n\t\t\t\t\treturn await client(queryString, params as any[], 'all', typings);\n\t\t\t\t});\n\n\t\t\t\treturn rows;\n\t\t\t});\n\n\t\t\treturn tracer.startActiveSpan('drizzle.mapResponse', () => {\n\t\t\t\treturn customResultMapper\n\t\t\t\t\t? customResultMapper(rows)\n\t\t\t\t\t: rows.map((row) => mapResultRow<T['execute']>(fields!, row, joinsNotNullableMap));\n\t\t\t});\n\t\t});\n\t}\n\n\tasync all() {\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode(): boolean {\n\t\treturn this._isResponseInArrayMode;\n\t}\n}\n\nexport interface PgRemoteQueryResultHKT extends PgQueryResultHKT {\n\ttype: Assume<this['row'], {\n\t\t[column: string]: any;\n\t}>[];\n}\n"], "mappings": "AAAA,SAAqB,iBAAiB;AAEtC,SAAS,kBAAkB;AAE3B,SAAS,kBAAkB;AAE3B,SAAS,qBAAqB;AAG9B,SAAS,mBAAmB,mBAAmB,iBAAiB;AAGhE,SAAS,wBAAwB;AACjC,SAAS,cAAc;AACvB,SAAsB,oBAAoB;AAQnC,MAAM,wBAGH,UAAwD;AAAA,EAMjE,YACS,QACR,SACQ,QACR,UAAkC,CAAC,GAClC;AACD,UAAM,OAAO;AALL;AAEA;AAIR,SAAK,SAAS,QAAQ,UAAU,IAAI,WAAW;AAC/C,SAAK,QAAQ,QAAQ,SAAS,IAAI,UAAU;AAAA,EAC7C;AAAA,EAdA,QAA0B,UAAU,IAAY;AAAA,EAExC;AAAA,EACA;AAAA,EAaR,aACC,OACA,QACA,MACA,uBACA,oBACA,eAIA,aACmB;AACnB,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAe,YACd,cACA,SACa;AACb,UAAM,IAAI,MAAM,6DAA6D;AAAA,EAC9E;AACD;AAEO,MAAM,2BAGH,cAA4D;AAAA,EACrE,QAA0B,UAAU,IAAY;AAAA,EAEhD,MAAe,YACd,cACa;AACb,UAAM,IAAI,MAAM,6DAA6D;AAAA,EAC9E;AACD;AAEO,MAAM,sBAAqD,kBAAqB;AAAA,EAGtF,YACS,QACA,aACA,QACA,SACA,QACR,OACA,eAIA,aACQ,QACA,wBACA,oBACP;AACD,UAAM,EAAE,KAAK,aAAa,OAAO,GAAG,OAAO,eAAe,WAAW;AAf7D;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AAAA,EAGT;AAAA,EAnBA,QAA0B,UAAU,IAAY;AAAA,EAqBhD,MAAM,QAAQ,oBAAyD,CAAC,GAA0B;AACjG,WAAO,OAAO,gBAAgB,mBAAmB,OAAO,SAAS;AAChE,YAAM,SAAS,iBAAiB,KAAK,QAAQ,iBAAiB;AAC9D,YAAM,EAAE,QAAQ,QAAQ,aAAa,qBAAqB,oBAAoB,QAAQ,QAAQ,IAAI;AAElG,YAAM,cAAc;AAAA,QACnB,sBAAsB;AAAA,QACtB,wBAAwB,KAAK,UAAU,MAAM;AAAA,MAC9C,CAAC;AAED,aAAO,SAAS,aAAa,MAAM;AAEnC,UAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,eAAO,OAAO,gBAAgB,0BAA0B,YAAY;AACnE,gBAAM,EAAE,MAAAA,MAAK,IAAI,MAAM,KAAK,eAAe,aAAa,QAAQ,YAAY;AAC3E,mBAAO,MAAM,OAAO,aAAa,QAAiB,WAAW,OAAO;AAAA,UACrE,CAAC;AAED,iBAAOA;AAAA,QACR,CAAC;AAAA,MACF;AAEA,YAAM,OAAO,MAAM,OAAO,gBAAgB,0BAA0B,YAAY;AAC/E,cAAM,cAAc;AAAA,UACnB,sBAAsB;AAAA,UACtB,wBAAwB,KAAK,UAAU,MAAM;AAAA,QAC9C,CAAC;AAED,cAAM,EAAE,MAAAA,MAAK,IAAI,MAAM,KAAK,eAAe,aAAa,QAAQ,YAAY;AAC3E,iBAAO,MAAM,OAAO,aAAa,QAAiB,OAAO,OAAO;AAAA,QACjE,CAAC;AAED,eAAOA;AAAA,MACR,CAAC;AAED,aAAO,OAAO,gBAAgB,uBAAuB,MAAM;AAC1D,eAAO,qBACJ,mBAAmB,IAAI,IACvB,KAAK,IAAI,CAAC,QAAQ,aAA2B,QAAS,KAAK,mBAAmB,CAAC;AAAA,MACnF,CAAC;AAAA,IACF,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,MAAM;AAAA,EACZ;AAAA;AAAA,EAGA,wBAAiC;AAChC,WAAO,KAAK;AAAA,EACb;AACD;", "names": ["rows"]}
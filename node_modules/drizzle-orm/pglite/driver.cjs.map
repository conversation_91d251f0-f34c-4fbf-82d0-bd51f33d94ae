{"version": 3, "sources": ["../../src/pglite/driver.ts"], "sourcesContent": ["import { PGlite, type PGliteOptions } from '@electric-sql/pglite';\nimport type { Cache } from '~/cache/core/cache.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport { PgDatabase } from '~/pg-core/db.ts';\nimport { PgDialect } from '~/pg-core/dialect.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { type DrizzleConfig, isConfig } from '~/utils.ts';\nimport type { PgliteClient, PgliteQueryResultHKT } from './session.ts';\nimport { PgliteSession } from './session.ts';\n\nexport interface PgDriverOptions {\n\tlogger?: Logger;\n\tcache?: Cache;\n}\n\nexport class PgliteDriver {\n\tstatic readonly [entityKind]: string = 'PgliteDriver';\n\n\tconstructor(\n\t\tprivate client: PgliteClient,\n\t\tprivate dialect: PgDialect,\n\t\tprivate options: PgDriverOptions = {},\n\t) {\n\t}\n\n\tcreateSession(\n\t\tschema: RelationalSchemaConfig<TablesRelationalConfig> | undefined,\n\t): PgliteSession<Record<string, unknown>, TablesRelationalConfig> {\n\t\treturn new PgliteSession(this.client, this.dialect, schema, {\n\t\t\tlogger: this.options.logger,\n\t\t\tcache: this.options.cache,\n\t\t});\n\t}\n}\n\nexport class PgliteDatabase<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> extends PgDatabase<PgliteQueryResultHKT, TSchema> {\n\tstatic override readonly [entityKind]: string = 'PgliteDatabase';\n}\n\nfunction construct<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tclient: PgliteClient,\n\tconfig: DrizzleConfig<TSchema> = {},\n): PgliteDatabase<TSchema> & {\n\t$client: PgliteClient;\n} {\n\tconst dialect = new PgDialect({ casing: config.casing });\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst driver = new PgliteDriver(client, dialect, { logger, cache: config.cache });\n\tconst session = driver.createSession(schema);\n\tconst db = new PgliteDatabase(dialect, session, schema as any) as PgliteDatabase<TSchema>;\n\t(<any> db).$client = client;\n\t(<any> db).$cache = config.cache;\n\tif ((<any> db).$cache) {\n\t\t(<any> db).$cache['invalidate'] = config.cache?.onMutate;\n\t}\n\t// (<any> db).$cache = { invalidate: (<any> config).cache?.onMutate };\n\t// if (config.cache) {\n\t// \tfor (\n\t// \t\tconst key of Object.getOwnPropertyNames(Object.getPrototypeOf(config.cache)).filter((key) =>\n\t// \t\t\tkey !== 'constructor'\n\t// \t\t)\n\t// \t) {\n\t// \t\t(<any> db).$cache[key as keyof typeof config.cache] = (<any> config).cache[key];\n\t// \t}\n\t// }\n\n\treturn db as any;\n}\n\nexport function drizzle<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n\tTClient extends PGlite = PGlite,\n>(\n\t...params:\n\t\t| []\n\t\t| [\n\t\t\tTClient | string,\n\t\t]\n\t\t| [\n\t\t\tTClient | string,\n\t\t\tDrizzleConfig<TSchema>,\n\t\t]\n\t\t| [\n\t\t\t(\n\t\t\t\t& DrizzleConfig<TSchema>\n\t\t\t\t& ({\n\t\t\t\t\tconnection?: (PGliteOptions & { dataDir?: string }) | string;\n\t\t\t\t} | {\n\t\t\t\t\tclient: TClient;\n\t\t\t\t})\n\t\t\t),\n\t\t]\n): PgliteDatabase<TSchema> & {\n\t$client: TClient;\n} {\n\tif (params[0] === undefined || typeof params[0] === 'string') {\n\t\tconst instance = new PGlite(params[0]);\n\t\treturn construct(instance, params[1]) as any;\n\t}\n\n\tif (isConfig(params[0])) {\n\t\tconst { connection, client, ...drizzleConfig } = params[0] as {\n\t\t\tconnection?: PGliteOptions & { dataDir: string };\n\t\t\tclient?: TClient;\n\t\t} & DrizzleConfig<TSchema>;\n\n\t\tif (client) return construct(client, drizzleConfig) as any;\n\n\t\tif (typeof connection === 'object') {\n\t\t\tconst { dataDir, ...options } = connection;\n\n\t\t\tconst instance = new PGlite(dataDir, options);\n\n\t\t\treturn construct(instance, drizzleConfig) as any;\n\t\t}\n\n\t\tconst instance = new PGlite(connection);\n\n\t\treturn construct(instance, drizzleConfig) as any;\n\t}\n\n\treturn construct(params[0] as TClient, params[1] as DrizzleConfig<TSchema> | undefined) as any;\n}\n\nexport namespace drizzle {\n\texport function mock<TSchema extends Record<string, unknown> = Record<string, never>>(\n\t\tconfig?: DrizzleConfig<TSchema>,\n\t): PgliteDatabase<TSchema> & {\n\t\t$client: '$client is not available on drizzle.mock()';\n\t} {\n\t\treturn construct({} as any, config) as any;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2C;AAE3C,oBAA2B;AAE3B,oBAA8B;AAC9B,gBAA2B;AAC3B,qBAA0B;AAC1B,uBAKO;AACP,mBAA6C;AAE7C,qBAA8B;AAOvB,MAAM,aAAa;AAAA,EAGzB,YACS,QACA,SACA,UAA2B,CAAC,GACnC;AAHO;AACA;AACA;AAAA,EAET;AAAA,EAPA,QAAiB,wBAAU,IAAY;AAAA,EASvC,cACC,QACiE;AACjE,WAAO,IAAI,6BAAc,KAAK,QAAQ,KAAK,SAAS,QAAQ;AAAA,MAC3D,QAAQ,KAAK,QAAQ;AAAA,MACrB,OAAO,KAAK,QAAQ;AAAA,IACrB,CAAC;AAAA,EACF;AACD;AAEO,MAAM,uBAEH,qBAA0C;AAAA,EACnD,QAA0B,wBAAU,IAAY;AACjD;AAEA,SAAS,UACR,QACA,SAAiC,CAAC,GAGjC;AACD,QAAM,UAAU,IAAI,yBAAU,EAAE,QAAQ,OAAO,OAAO,CAAC;AACvD,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,4BAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,mBAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,SAAS,IAAI,aAAa,QAAQ,SAAS,EAAE,QAAQ,OAAO,OAAO,MAAM,CAAC;AAChF,QAAM,UAAU,OAAO,cAAc,MAAM;AAC3C,QAAM,KAAK,IAAI,eAAe,SAAS,SAAS,MAAa;AAC7D,EAAO,GAAI,UAAU;AACrB,EAAO,GAAI,SAAS,OAAO;AAC3B,MAAW,GAAI,QAAQ;AACtB,IAAO,GAAI,OAAO,YAAY,IAAI,OAAO,OAAO;AAAA,EACjD;AAYA,SAAO;AACR;AAEO,SAAS,WAIZ,QAqBF;AACD,MAAI,OAAO,CAAC,MAAM,UAAa,OAAO,OAAO,CAAC,MAAM,UAAU;AAC7D,UAAM,WAAW,IAAI,qBAAO,OAAO,CAAC,CAAC;AACrC,WAAO,UAAU,UAAU,OAAO,CAAC,CAAC;AAAA,EACrC;AAEA,UAAI,uBAAS,OAAO,CAAC,CAAC,GAAG;AACxB,UAAM,EAAE,YAAY,QAAQ,GAAG,cAAc,IAAI,OAAO,CAAC;AAKzD,QAAI,OAAQ,QAAO,UAAU,QAAQ,aAAa;AAElD,QAAI,OAAO,eAAe,UAAU;AACnC,YAAM,EAAE,SAAS,GAAG,QAAQ,IAAI;AAEhC,YAAMA,YAAW,IAAI,qBAAO,SAAS,OAAO;AAE5C,aAAO,UAAUA,WAAU,aAAa;AAAA,IACzC;AAEA,UAAM,WAAW,IAAI,qBAAO,UAAU;AAEtC,WAAO,UAAU,UAAU,aAAa;AAAA,EACzC;AAEA,SAAO,UAAU,OAAO,CAAC,GAAc,OAAO,CAAC,CAAuC;AACvF;AAAA,CAEO,CAAUC,aAAV;AACC,WAAS,KACf,QAGC;AACD,WAAO,UAAU,CAAC,GAAU,MAAM;AAAA,EACnC;AANO,EAAAA,SAAS;AAAA,GADA;", "names": ["instance", "drizzle"]}
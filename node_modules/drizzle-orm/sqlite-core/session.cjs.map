{"version": 3, "sources": ["../../src/sqlite-core/session.ts"], "sourcesContent": ["import { type Cache, hashQuery, NoopCache } from '~/cache/core/cache.ts';\nimport type { WithCacheConfig } from '~/cache/core/types.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport { <PERSON><PERSON><PERSON>Error, Dr<PERSON>zleQ<PERSON>yError, TransactionRollbackError } from '~/errors.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { TablesRelationalConfig } from '~/relations.ts';\nimport type { PreparedQuery } from '~/session.ts';\nimport type { Query, SQL } from '~/sql/sql.ts';\nimport type { SQLiteAsyncDialect, SQLiteSyncDialect } from '~/sqlite-core/dialect.ts';\nimport { BaseSQLiteDatabase } from './db.ts';\nimport type { SQLiteRaw } from './query-builders/raw.ts';\nimport type { SelectedFieldsOrdered } from './query-builders/select.types.ts';\n\nexport interface PreparedQueryConfig {\n\ttype: 'sync' | 'async';\n\trun: unknown;\n\tall: unknown;\n\tget: unknown;\n\tvalues: unknown;\n\texecute: unknown;\n}\n\nexport class ExecuteResultSync<T> extends QueryPromise<T> {\n\tstatic override readonly [entityKind]: string = 'ExecuteResultSync';\n\n\tconstructor(private resultCb: () => T) {\n\t\tsuper();\n\t}\n\n\toverride async execute(): Promise<T> {\n\t\treturn this.resultCb();\n\t}\n\n\tsync(): T {\n\t\treturn this.resultCb();\n\t}\n}\n\nexport type ExecuteResult<TType extends 'sync' | 'async', TResult> = TType extends 'async' ? Promise<TResult>\n\t: ExecuteResultSync<TResult>;\n\nexport abstract class SQLitePreparedQuery<T extends PreparedQueryConfig> implements PreparedQuery {\n\tstatic readonly [entityKind]: string = 'PreparedQuery';\n\n\t/** @internal */\n\tjoinsNotNullableMap?: Record<string, boolean>;\n\n\tconstructor(\n\t\tprivate mode: 'sync' | 'async',\n\t\tprivate executeMethod: SQLiteExecuteMethod,\n\t\tprotected query: Query,\n\t\tprivate cache?: Cache,\n\t\t// per query related metadata\n\t\tprivate queryMetadata?: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t} | undefined,\n\t\t// config that was passed through $withCache\n\t\tprivate cacheConfig?: WithCacheConfig,\n\t) {\n\t\t// it means that no $withCache options were passed and it should be just enabled\n\t\tif (cache && cache.strategy() === 'all' && cacheConfig === undefined) {\n\t\t\tthis.cacheConfig = { enable: true, autoInvalidate: true };\n\t\t}\n\t\tif (!this.cacheConfig?.enable) {\n\t\t\tthis.cacheConfig = undefined;\n\t\t}\n\t}\n\n\t/** @internal */\n\tprotected async queryWithCache<T>(\n\t\tqueryString: string,\n\t\tparams: any[],\n\t\tquery: () => Promise<T>,\n\t): Promise<T> {\n\t\tif (this.cache === undefined || is(this.cache, NoopCache) || this.queryMetadata === undefined) {\n\t\t\ttry {\n\t\t\t\treturn await query();\n\t\t\t} catch (e) {\n\t\t\t\tthrow new DrizzleQueryError(queryString, params, e as Error);\n\t\t\t}\n\t\t}\n\n\t\t// don't do any mutations, if globally is false\n\t\tif (this.cacheConfig && !this.cacheConfig.enable) {\n\t\t\ttry {\n\t\t\t\treturn await query();\n\t\t\t} catch (e) {\n\t\t\t\tthrow new DrizzleQueryError(queryString, params, e as Error);\n\t\t\t}\n\t\t}\n\n\t\t// For mutate queries, we should query the database, wait for a response, and then perform invalidation\n\t\tif (\n\t\t\t(\n\t\t\t\tthis.queryMetadata.type === 'insert' || this.queryMetadata.type === 'update'\n\t\t\t\t|| this.queryMetadata.type === 'delete'\n\t\t\t) && this.queryMetadata.tables.length > 0\n\t\t) {\n\t\t\ttry {\n\t\t\t\tconst [res] = await Promise.all([\n\t\t\t\t\tquery(),\n\t\t\t\t\tthis.cache.onMutate({ tables: this.queryMetadata.tables }),\n\t\t\t\t]);\n\t\t\t\treturn res;\n\t\t\t} catch (e) {\n\t\t\t\tthrow new DrizzleQueryError(queryString, params, e as Error);\n\t\t\t}\n\t\t}\n\n\t\t// don't do any reads if globally disabled\n\t\tif (!this.cacheConfig) {\n\t\t\ttry {\n\t\t\t\treturn await query();\n\t\t\t} catch (e) {\n\t\t\t\tthrow new DrizzleQueryError(queryString, params, e as Error);\n\t\t\t}\n\t\t}\n\n\t\tif (this.queryMetadata.type === 'select') {\n\t\t\tconst fromCache = await this.cache.get(\n\t\t\t\tthis.cacheConfig.tag ?? await hashQuery(queryString, params),\n\t\t\t\tthis.queryMetadata.tables,\n\t\t\t\tthis.cacheConfig.tag !== undefined,\n\t\t\t\tthis.cacheConfig.autoInvalidate,\n\t\t\t);\n\t\t\tif (fromCache === undefined) {\n\t\t\t\tlet result;\n\t\t\t\ttry {\n\t\t\t\t\tresult = await query();\n\t\t\t\t} catch (e) {\n\t\t\t\t\tthrow new DrizzleQueryError(queryString, params, e as Error);\n\t\t\t\t}\n\n\t\t\t\t// put actual key\n\t\t\t\tawait this.cache.put(\n\t\t\t\t\tthis.cacheConfig.tag ?? await hashQuery(queryString, params),\n\t\t\t\t\tresult,\n\t\t\t\t\t// make sure we send tables that were used in a query only if user wants to invalidate it on each write\n\t\t\t\t\tthis.cacheConfig.autoInvalidate ? this.queryMetadata.tables : [],\n\t\t\t\t\tthis.cacheConfig.tag !== undefined,\n\t\t\t\t\tthis.cacheConfig.config,\n\t\t\t\t);\n\t\t\t\t// put flag if we should invalidate or not\n\t\t\t\treturn result;\n\t\t\t}\n\n\t\t\treturn fromCache as unknown as T;\n\t\t}\n\t\ttry {\n\t\t\treturn await query();\n\t\t} catch (e) {\n\t\t\tthrow new DrizzleQueryError(queryString, params, e as Error);\n\t\t}\n\t}\n\n\tgetQuery(): Query {\n\t\treturn this.query;\n\t}\n\n\tabstract run(placeholderValues?: Record<string, unknown>): Result<T['type'], T['run']>;\n\n\tmapRunResult(result: unknown, _isFromBatch?: boolean): unknown {\n\t\treturn result;\n\t}\n\n\tabstract all(placeholderValues?: Record<string, unknown>): Result<T['type'], T['all']>;\n\n\tmapAllResult(_result: unknown, _isFromBatch?: boolean): unknown {\n\t\tthrow new Error('Not implemented');\n\t}\n\n\tabstract get(placeholderValues?: Record<string, unknown>): Result<T['type'], T['get']>;\n\n\tmapGetResult(_result: unknown, _isFromBatch?: boolean): unknown {\n\t\tthrow new Error('Not implemented');\n\t}\n\n\tabstract values(placeholderValues?: Record<string, unknown>): Result<T['type'], T['values']>;\n\n\texecute(placeholderValues?: Record<string, unknown>): ExecuteResult<T['type'], T['execute']> {\n\t\tif (this.mode === 'async') {\n\t\t\treturn this[this.executeMethod](placeholderValues) as ExecuteResult<T['type'], T['execute']>;\n\t\t}\n\t\treturn new ExecuteResultSync(() => this[this.executeMethod](placeholderValues));\n\t}\n\n\tmapResult(response: unknown, isFromBatch?: boolean) {\n\t\tswitch (this.executeMethod) {\n\t\t\tcase 'run': {\n\t\t\t\treturn this.mapRunResult(response, isFromBatch);\n\t\t\t}\n\t\t\tcase 'all': {\n\t\t\t\treturn this.mapAllResult(response, isFromBatch);\n\t\t\t}\n\t\t\tcase 'get': {\n\t\t\t\treturn this.mapGetResult(response, isFromBatch);\n\t\t\t}\n\t\t}\n\t}\n\n\t/** @internal */\n\tabstract isResponseInArrayMode(): boolean;\n}\n\nexport interface SQLiteTransactionConfig {\n\tbehavior?: 'deferred' | 'immediate' | 'exclusive';\n}\n\nexport type SQLiteExecuteMethod = 'run' | 'all' | 'get';\n\nexport abstract class SQLiteSession<\n\tTResultKind extends 'sync' | 'async',\n\tTRunResult,\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> {\n\tstatic readonly [entityKind]: string = 'SQLiteSession';\n\n\tconstructor(\n\t\t/** @internal */\n\t\treadonly dialect: { sync: SQLiteSyncDialect; async: SQLiteAsyncDialect }[TResultKind],\n\t) {}\n\n\tabstract prepareQuery(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\texecuteMethod: SQLiteExecuteMethod,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][], mapColumnValue?: (value: unknown) => unknown) => unknown,\n\t\tqueryMetadata?: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t},\n\t\tcacheConfig?: WithCacheConfig,\n\t): SQLitePreparedQuery<PreparedQueryConfig & { type: TResultKind }>;\n\n\tprepareOneTimeQuery(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\texecuteMethod: SQLiteExecuteMethod,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][], mapColumnValue?: (value: unknown) => unknown) => unknown,\n\t\tqueryMetadata?: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t},\n\t\tcacheConfig?: WithCacheConfig,\n\t): SQLitePreparedQuery<PreparedQueryConfig & { type: TResultKind }> {\n\t\treturn this.prepareQuery(\n\t\t\tquery,\n\t\t\tfields,\n\t\t\texecuteMethod,\n\t\t\tisResponseInArrayMode,\n\t\t\tcustomResultMapper,\n\t\t\tqueryMetadata,\n\t\t\tcacheConfig,\n\t\t);\n\t}\n\n\tabstract transaction<T>(\n\t\ttransaction: (tx: SQLiteTransaction<TResultKind, TRunResult, TFullSchema, TSchema>) => Result<TResultKind, T>,\n\t\tconfig?: SQLiteTransactionConfig,\n\t): Result<TResultKind, T>;\n\n\trun(query: SQL): Result<TResultKind, TRunResult> {\n\t\tconst staticQuery = this.dialect.sqlToQuery(query);\n\t\ttry {\n\t\t\treturn this.prepareOneTimeQuery(staticQuery, undefined, 'run', false).run() as Result<TResultKind, TRunResult>;\n\t\t} catch (err) {\n\t\t\tthrow new DrizzleError({ cause: err, message: `Failed to run the query '${staticQuery.sql}'` });\n\t\t}\n\t}\n\n\t/** @internal */\n\textractRawRunValueFromBatchResult(result: unknown) {\n\t\treturn result;\n\t}\n\n\tall<T = unknown>(query: SQL): Result<TResultKind, T[]> {\n\t\treturn this.prepareOneTimeQuery(this.dialect.sqlToQuery(query), undefined, 'run', false).all() as Result<\n\t\t\tTResultKind,\n\t\t\tT[]\n\t\t>;\n\t}\n\n\t/** @internal */\n\textractRawAllValueFromBatchResult(_result: unknown): unknown {\n\t\tthrow new Error('Not implemented');\n\t}\n\n\tget<T = unknown>(query: SQL): Result<TResultKind, T> {\n\t\treturn this.prepareOneTimeQuery(this.dialect.sqlToQuery(query), undefined, 'run', false).get() as Result<\n\t\t\tTResultKind,\n\t\t\tT\n\t\t>;\n\t}\n\n\t/** @internal */\n\textractRawGetValueFromBatchResult(_result: unknown): unknown {\n\t\tthrow new Error('Not implemented');\n\t}\n\n\tvalues<T extends any[] = unknown[]>(\n\t\tquery: SQL,\n\t): Result<TResultKind, T[]> {\n\t\treturn this.prepareOneTimeQuery(this.dialect.sqlToQuery(query), undefined, 'run', false).values() as Result<\n\t\t\tTResultKind,\n\t\t\tT[]\n\t\t>;\n\t}\n\n\tasync count(sql: SQL) {\n\t\tconst result = await this.values(sql) as [[number]];\n\n\t\treturn result[0][0];\n\t}\n\n\t/** @internal */\n\textractRawValuesValueFromBatchResult(_result: unknown): unknown {\n\t\tthrow new Error('Not implemented');\n\t}\n}\n\nexport type Result<TKind extends 'sync' | 'async', TResult> = { sync: TResult; async: Promise<TResult> }[TKind];\n\nexport type DBResult<TKind extends 'sync' | 'async', TResult> = { sync: TResult; async: SQLiteRaw<TResult> }[TKind];\n\nexport abstract class SQLiteTransaction<\n\tTResultType extends 'sync' | 'async',\n\tTRunResult,\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends BaseSQLiteDatabase<TResultType, TRunResult, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'SQLiteTransaction';\n\n\tconstructor(\n\t\tresultType: TResultType,\n\t\tdialect: { sync: SQLiteSyncDialect; async: SQLiteAsyncDialect }[TResultType],\n\t\tsession: SQLiteSession<TResultType, TRunResult, TFullSchema, TSchema>,\n\t\tprotected schema: {\n\t\t\tfullSchema: Record<string, unknown>;\n\t\t\tschema: TSchema;\n\t\t\ttableNamesMap: Record<string, string>;\n\t\t} | undefined,\n\t\tprotected readonly nestedIndex = 0,\n\t) {\n\t\tsuper(resultType, dialect, session, schema);\n\t}\n\n\trollback(): never {\n\t\tthrow new TransactionRollbackError();\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAiD;AAEjD,oBAA+B;AAC/B,oBAA0E;AAC1E,2BAA6B;AAK7B,gBAAmC;AAa5B,MAAM,0BAA6B,kCAAgB;AAAA,EAGzD,YAAoB,UAAmB;AACtC,UAAM;AADa;AAAA,EAEpB;AAAA,EAJA,QAA0B,wBAAU,IAAY;AAAA,EAMhD,MAAe,UAAsB;AACpC,WAAO,KAAK,SAAS;AAAA,EACtB;AAAA,EAEA,OAAU;AACT,WAAO,KAAK,SAAS;AAAA,EACtB;AACD;AAKO,MAAe,oBAA4E;AAAA,EAMjG,YACS,MACA,eACE,OACF,OAEA,eAKA,aACP;AAXO;AACA;AACE;AACF;AAEA;AAKA;AAGR,QAAI,SAAS,MAAM,SAAS,MAAM,SAAS,gBAAgB,QAAW;AACrE,WAAK,cAAc,EAAE,QAAQ,MAAM,gBAAgB,KAAK;AAAA,IACzD;AACA,QAAI,CAAC,KAAK,aAAa,QAAQ;AAC9B,WAAK,cAAc;AAAA,IACpB;AAAA,EACD;AAAA,EAzBA,QAAiB,wBAAU,IAAY;AAAA;AAAA,EAGvC;AAAA;AAAA,EAyBA,MAAgB,eACf,aACA,QACA,OACa;AACb,QAAI,KAAK,UAAU,cAAa,kBAAG,KAAK,OAAO,sBAAS,KAAK,KAAK,kBAAkB,QAAW;AAC9F,UAAI;AACH,eAAO,MAAM,MAAM;AAAA,MACpB,SAAS,GAAG;AACX,cAAM,IAAI,gCAAkB,aAAa,QAAQ,CAAU;AAAA,MAC5D;AAAA,IACD;AAGA,QAAI,KAAK,eAAe,CAAC,KAAK,YAAY,QAAQ;AACjD,UAAI;AACH,eAAO,MAAM,MAAM;AAAA,MACpB,SAAS,GAAG;AACX,cAAM,IAAI,gCAAkB,aAAa,QAAQ,CAAU;AAAA,MAC5D;AAAA,IACD;AAGA,SAEE,KAAK,cAAc,SAAS,YAAY,KAAK,cAAc,SAAS,YACjE,KAAK,cAAc,SAAS,aAC3B,KAAK,cAAc,OAAO,SAAS,GACvC;AACD,UAAI;AACH,cAAM,CAAC,GAAG,IAAI,MAAM,QAAQ,IAAI;AAAA,UAC/B,MAAM;AAAA,UACN,KAAK,MAAM,SAAS,EAAE,QAAQ,KAAK,cAAc,OAAO,CAAC;AAAA,QAC1D,CAAC;AACD,eAAO;AAAA,MACR,SAAS,GAAG;AACX,cAAM,IAAI,gCAAkB,aAAa,QAAQ,CAAU;AAAA,MAC5D;AAAA,IACD;AAGA,QAAI,CAAC,KAAK,aAAa;AACtB,UAAI;AACH,eAAO,MAAM,MAAM;AAAA,MACpB,SAAS,GAAG;AACX,cAAM,IAAI,gCAAkB,aAAa,QAAQ,CAAU;AAAA,MAC5D;AAAA,IACD;AAEA,QAAI,KAAK,cAAc,SAAS,UAAU;AACzC,YAAM,YAAY,MAAM,KAAK,MAAM;AAAA,QAClC,KAAK,YAAY,OAAO,UAAM,wBAAU,aAAa,MAAM;AAAA,QAC3D,KAAK,cAAc;AAAA,QACnB,KAAK,YAAY,QAAQ;AAAA,QACzB,KAAK,YAAY;AAAA,MAClB;AACA,UAAI,cAAc,QAAW;AAC5B,YAAI;AACJ,YAAI;AACH,mBAAS,MAAM,MAAM;AAAA,QACtB,SAAS,GAAG;AACX,gBAAM,IAAI,gCAAkB,aAAa,QAAQ,CAAU;AAAA,QAC5D;AAGA,cAAM,KAAK,MAAM;AAAA,UAChB,KAAK,YAAY,OAAO,UAAM,wBAAU,aAAa,MAAM;AAAA,UAC3D;AAAA;AAAA,UAEA,KAAK,YAAY,iBAAiB,KAAK,cAAc,SAAS,CAAC;AAAA,UAC/D,KAAK,YAAY,QAAQ;AAAA,UACzB,KAAK,YAAY;AAAA,QAClB;AAEA,eAAO;AAAA,MACR;AAEA,aAAO;AAAA,IACR;AACA,QAAI;AACH,aAAO,MAAM,MAAM;AAAA,IACpB,SAAS,GAAG;AACX,YAAM,IAAI,gCAAkB,aAAa,QAAQ,CAAU;AAAA,IAC5D;AAAA,EACD;AAAA,EAEA,WAAkB;AACjB,WAAO,KAAK;AAAA,EACb;AAAA,EAIA,aAAa,QAAiB,cAAiC;AAC9D,WAAO;AAAA,EACR;AAAA,EAIA,aAAa,SAAkB,cAAiC;AAC/D,UAAM,IAAI,MAAM,iBAAiB;AAAA,EAClC;AAAA,EAIA,aAAa,SAAkB,cAAiC;AAC/D,UAAM,IAAI,MAAM,iBAAiB;AAAA,EAClC;AAAA,EAIA,QAAQ,mBAAqF;AAC5F,QAAI,KAAK,SAAS,SAAS;AAC1B,aAAO,KAAK,KAAK,aAAa,EAAE,iBAAiB;AAAA,IAClD;AACA,WAAO,IAAI,kBAAkB,MAAM,KAAK,KAAK,aAAa,EAAE,iBAAiB,CAAC;AAAA,EAC/E;AAAA,EAEA,UAAU,UAAmB,aAAuB;AACnD,YAAQ,KAAK,eAAe;AAAA,MAC3B,KAAK,OAAO;AACX,eAAO,KAAK,aAAa,UAAU,WAAW;AAAA,MAC/C;AAAA,MACA,KAAK,OAAO;AACX,eAAO,KAAK,aAAa,UAAU,WAAW;AAAA,MAC/C;AAAA,MACA,KAAK,OAAO;AACX,eAAO,KAAK,aAAa,UAAU,WAAW;AAAA,MAC/C;AAAA,IACD;AAAA,EACD;AAID;AAQO,MAAe,cAKpB;AAAA,EAGD,YAEU,SACR;AADQ;AAAA,EACP;AAAA,EALH,QAAiB,wBAAU,IAAY;AAAA,EAoBvC,oBACC,OACA,QACA,eACA,uBACA,oBACA,eAIA,aACmE;AACnE,WAAO,KAAK;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAOA,IAAI,OAA6C;AAChD,UAAM,cAAc,KAAK,QAAQ,WAAW,KAAK;AACjD,QAAI;AACH,aAAO,KAAK,oBAAoB,aAAa,QAAW,OAAO,KAAK,EAAE,IAAI;AAAA,IAC3E,SAAS,KAAK;AACb,YAAM,IAAI,2BAAa,EAAE,OAAO,KAAK,SAAS,4BAA4B,YAAY,GAAG,IAAI,CAAC;AAAA,IAC/F;AAAA,EACD;AAAA;AAAA,EAGA,kCAAkC,QAAiB;AAClD,WAAO;AAAA,EACR;AAAA,EAEA,IAAiB,OAAsC;AACtD,WAAO,KAAK,oBAAoB,KAAK,QAAQ,WAAW,KAAK,GAAG,QAAW,OAAO,KAAK,EAAE,IAAI;AAAA,EAI9F;AAAA;AAAA,EAGA,kCAAkC,SAA2B;AAC5D,UAAM,IAAI,MAAM,iBAAiB;AAAA,EAClC;AAAA,EAEA,IAAiB,OAAoC;AACpD,WAAO,KAAK,oBAAoB,KAAK,QAAQ,WAAW,KAAK,GAAG,QAAW,OAAO,KAAK,EAAE,IAAI;AAAA,EAI9F;AAAA;AAAA,EAGA,kCAAkC,SAA2B;AAC5D,UAAM,IAAI,MAAM,iBAAiB;AAAA,EAClC;AAAA,EAEA,OACC,OAC2B;AAC3B,WAAO,KAAK,oBAAoB,KAAK,QAAQ,WAAW,KAAK,GAAG,QAAW,OAAO,KAAK,EAAE,OAAO;AAAA,EAIjG;AAAA,EAEA,MAAM,MAAM,KAAU;AACrB,UAAM,SAAS,MAAM,KAAK,OAAO,GAAG;AAEpC,WAAO,OAAO,CAAC,EAAE,CAAC;AAAA,EACnB;AAAA;AAAA,EAGA,qCAAqC,SAA2B;AAC/D,UAAM,IAAI,MAAM,iBAAiB;AAAA,EAClC;AACD;AAMO,MAAe,0BAKZ,6BAAkE;AAAA,EAG3E,YACC,YACA,SACA,SACU,QAKS,cAAc,GAChC;AACD,UAAM,YAAY,SAAS,SAAS,MAAM;AAPhC;AAKS;AAAA,EAGpB;AAAA,EAdA,QAA0B,wBAAU,IAAY;AAAA,EAgBhD,WAAkB;AACjB,UAAM,IAAI,uCAAyB;AAAA,EACpC;AACD;", "names": []}
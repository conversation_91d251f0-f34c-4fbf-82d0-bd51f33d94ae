{"version": 3, "sources": ["../../../src/sqlite-core/query-builders/update.ts"], "sourcesContent": ["import type { GetColumnData } from '~/column.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport type { JoinType, SelectResultFields } from '~/query-builders/select.types.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport type { Placeholder, Query, SQL, SQLWrapper } from '~/sql/sql.ts';\nimport type { SQLiteDialect } from '~/sqlite-core/dialect.ts';\nimport type { SQLitePreparedQuery, SQLiteSession } from '~/sqlite-core/session.ts';\nimport { SQLiteTable } from '~/sqlite-core/table.ts';\nimport { Subquery } from '~/subquery.ts';\nimport { Table } from '~/table.ts';\nimport {\n\ttype DrizzleTypeError,\n\tgetTableLikeName,\n\tmapUpdateSet,\n\torderSelectedFields,\n\ttype UpdateSet,\n\ttype ValueOrArray,\n} from '~/utils.ts';\nimport { ViewBaseConfig } from '~/view-common.ts';\nimport type { SQLiteColumn } from '../columns/common.ts';\nimport { extractUsedTable } from '../utils.ts';\nimport { SQLiteViewBase } from '../view-base.ts';\nimport type { SelectedFields, SelectedFieldsOrdered, SQLiteSelectJoinConfig } from './select.types.ts';\n\nexport interface SQLiteUpdateConfig {\n\twhere?: SQL | undefined;\n\tlimit?: number | Placeholder;\n\torderBy?: (SQLiteColumn | SQL | SQL.Aliased)[];\n\tset: UpdateSet;\n\ttable: SQLiteTable;\n\tfrom?: SQLiteTable | Subquery | SQLiteViewBase | SQL;\n\tjoins: SQLiteSelectJoinConfig[];\n\treturning?: SelectedFieldsOrdered;\n\twithList?: Subquery[];\n}\n\nexport type SQLiteUpdateSetSource<TTable extends SQLiteTable> =\n\t& {\n\t\t[Key in keyof TTable['$inferInsert']]?:\n\t\t\t| GetColumnData<TTable['_']['columns'][Key], 'query'>\n\t\t\t| SQL\n\t\t\t| SQLiteColumn\n\t\t\t| undefined;\n\t}\n\t& {};\n\nexport class SQLiteUpdateBuilder<\n\tTTable extends SQLiteTable,\n\tTResultType extends 'sync' | 'async',\n\tTRunResult,\n> {\n\tstatic readonly [entityKind]: string = 'SQLiteUpdateBuilder';\n\n\tdeclare readonly _: {\n\t\treadonly table: TTable;\n\t};\n\n\tconstructor(\n\t\tprotected table: TTable,\n\t\tprotected session: SQLiteSession<any, any, any, any>,\n\t\tprotected dialect: SQLiteDialect,\n\t\tprivate withList?: Subquery[],\n\t) {}\n\n\tset(\n\t\tvalues: SQLiteUpdateSetSource<TTable>,\n\t): SQLiteUpdateWithout<\n\t\tSQLiteUpdateBase<TTable, TResultType, TRunResult>,\n\t\tfalse,\n\t\t'leftJoin' | 'rightJoin' | 'innerJoin' | 'fullJoin'\n\t> {\n\t\treturn new SQLiteUpdateBase(\n\t\t\tthis.table,\n\t\t\tmapUpdateSet(this.table, values),\n\t\t\tthis.session,\n\t\t\tthis.dialect,\n\t\t\tthis.withList,\n\t\t) as any;\n\t}\n}\n\nexport type SQLiteUpdateWithout<\n\tT extends AnySQLiteUpdate,\n\tTDynamic extends boolean,\n\tK extends keyof T & string,\n> = TDynamic extends true ? T : Omit<\n\tSQLiteUpdateBase<\n\t\tT['_']['table'],\n\t\tT['_']['resultType'],\n\t\tT['_']['runResult'],\n\t\tT['_']['from'],\n\t\tT['_']['returning'],\n\t\tTDynamic,\n\t\tT['_']['excludedMethods'] | K\n\t>,\n\tT['_']['excludedMethods'] | K\n>;\n\nexport type SQLiteUpdateWithJoins<\n\tT extends AnySQLiteUpdate,\n\tTDynamic extends boolean,\n\tTFrom extends SQLiteTable | Subquery | SQLiteViewBase | SQL,\n> = TDynamic extends true ? T : Omit<\n\tSQLiteUpdateBase<\n\t\tT['_']['table'],\n\t\tT['_']['resultType'],\n\t\tT['_']['runResult'],\n\t\tTFrom,\n\t\tT['_']['returning'],\n\t\tTDynamic,\n\t\tExclude<T['_']['excludedMethods'] | 'from', 'leftJoin' | 'rightJoin' | 'innerJoin' | 'fullJoin'>\n\t>,\n\tExclude<T['_']['excludedMethods'] | 'from', 'leftJoin' | 'rightJoin' | 'innerJoin' | 'fullJoin'>\n>;\n\nexport type SQLiteUpdateReturningAll<T extends AnySQLiteUpdate, TDynamic extends boolean> = SQLiteUpdateWithout<\n\tSQLiteUpdateBase<\n\t\tT['_']['table'],\n\t\tT['_']['resultType'],\n\t\tT['_']['runResult'],\n\t\tT['_']['from'],\n\t\tT['_']['table']['$inferSelect'],\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>,\n\tTDynamic,\n\t'returning'\n>;\n\nexport type SQLiteUpdateReturning<\n\tT extends AnySQLiteUpdate,\n\tTDynamic extends boolean,\n\tTSelectedFields extends SelectedFields,\n> = SQLiteUpdateWithout<\n\tSQLiteUpdateBase<\n\t\tT['_']['table'],\n\t\tT['_']['resultType'],\n\t\tT['_']['runResult'],\n\t\tT['_']['from'],\n\t\tSelectResultFields<TSelectedFields>,\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>,\n\tTDynamic,\n\t'returning'\n>;\n\nexport type SQLiteUpdateExecute<T extends AnySQLiteUpdate> = T['_']['returning'] extends undefined ? T['_']['runResult']\n\t: T['_']['returning'][];\n\nexport type SQLiteUpdatePrepare<T extends AnySQLiteUpdate> = SQLitePreparedQuery<\n\t{\n\t\ttype: T['_']['resultType'];\n\t\trun: T['_']['runResult'];\n\t\tall: T['_']['returning'] extends undefined ? DrizzleTypeError<'.all() cannot be used without .returning()'>\n\t\t\t: T['_']['returning'][];\n\t\tget: T['_']['returning'] extends undefined ? DrizzleTypeError<'.get() cannot be used without .returning()'>\n\t\t\t: T['_']['returning'];\n\t\tvalues: T['_']['returning'] extends undefined ? DrizzleTypeError<'.values() cannot be used without .returning()'>\n\t\t\t: any[][];\n\t\texecute: SQLiteUpdateExecute<T>;\n\t}\n>;\n\nexport type SQLiteUpdateJoinFn<\n\tT extends AnySQLiteUpdate,\n> = <\n\tTJoinedTable extends SQLiteTable | Subquery | SQLiteViewBase | SQL,\n>(\n\ttable: TJoinedTable,\n\ton:\n\t\t| (\n\t\t\t(\n\t\t\t\tupdateTable: T['_']['table']['_']['columns'],\n\t\t\t\tfrom: T['_']['from'] extends SQLiteTable ? T['_']['from']['_']['columns']\n\t\t\t\t\t: T['_']['from'] extends Subquery | SQLiteViewBase ? T['_']['from']['_']['selectedFields']\n\t\t\t\t\t: never,\n\t\t\t) => SQL | undefined\n\t\t)\n\t\t| SQL\n\t\t| undefined,\n) => T;\n\nexport type SQLiteUpdateDynamic<T extends AnySQLiteUpdate> = SQLiteUpdate<\n\tT['_']['table'],\n\tT['_']['resultType'],\n\tT['_']['runResult'],\n\tT['_']['returning']\n>;\n\nexport type SQLiteUpdate<\n\tTTable extends SQLiteTable = SQLiteTable,\n\tTResultType extends 'sync' | 'async' = 'sync' | 'async',\n\tTRunResult = any,\n\tTFrom extends SQLiteTable | Subquery | SQLiteViewBase | SQL | undefined = undefined,\n\tTReturning extends Record<string, unknown> | undefined = Record<string, unknown> | undefined,\n> = SQLiteUpdateBase<TTable, TResultType, TRunResult, TFrom, TReturning, true, never>;\n\nexport type AnySQLiteUpdate = SQLiteUpdateBase<any, any, any, any, any, any, any>;\n\nexport interface SQLiteUpdateBase<\n\tTTable extends SQLiteTable = SQLiteTable,\n\tTResultType extends 'sync' | 'async' = 'sync' | 'async',\n\tTRunResult = unknown,\n\tTFrom extends SQLiteTable | Subquery | SQLiteViewBase | SQL | undefined = undefined,\n\tTReturning = undefined,\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n> extends SQLWrapper, QueryPromise<TReturning extends undefined ? TRunResult : TReturning[]> {\n\treadonly _: {\n\t\treadonly dialect: 'sqlite';\n\t\treadonly table: TTable;\n\t\treadonly resultType: TResultType;\n\t\treadonly runResult: TRunResult;\n\t\treadonly from: TFrom;\n\t\treadonly returning: TReturning;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TReturning extends undefined ? TRunResult : TReturning[];\n\t};\n}\n\nexport class SQLiteUpdateBase<\n\tTTable extends SQLiteTable = SQLiteTable,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTResultType extends 'sync' | 'async' = 'sync' | 'async',\n\tTRunResult = unknown,\n\tTFrom extends SQLiteTable | Subquery | SQLiteViewBase | SQL | undefined = undefined,\n\tTReturning = undefined,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTDynamic extends boolean = false,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<TReturning extends undefined ? TRunResult : TReturning[]>\n\timplements RunnableQuery<TReturning extends undefined ? TRunResult : TReturning[], 'sqlite'>, SQLWrapper\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteUpdate';\n\n\t/** @internal */\n\tconfig: SQLiteUpdateConfig;\n\n\tconstructor(\n\t\ttable: TTable,\n\t\tset: UpdateSet,\n\t\tprivate session: SQLiteSession<any, any, any, any>,\n\t\tprivate dialect: SQLiteDialect,\n\t\twithList?: Subquery[],\n\t) {\n\t\tsuper();\n\t\tthis.config = { set, table, withList, joins: [] };\n\t}\n\n\tfrom<TFrom extends SQLiteTable | Subquery | SQLiteViewBase | SQL>(\n\t\tsource: TFrom,\n\t): SQLiteUpdateWithJoins<this, TDynamic, TFrom> {\n\t\tthis.config.from = source;\n\t\treturn this as any;\n\t}\n\n\tprivate createJoin<TJoinType extends JoinType>(\n\t\tjoinType: TJoinType,\n\t): SQLiteUpdateJoinFn<this> {\n\t\treturn ((\n\t\t\ttable: SQLiteTable | Subquery | SQLiteViewBase | SQL,\n\t\t\ton: ((updateTable: TTable, from: TFrom) => SQL | undefined) | SQL | undefined,\n\t\t) => {\n\t\t\tconst tableName = getTableLikeName(table);\n\n\t\t\tif (typeof tableName === 'string' && this.config.joins.some((join) => join.alias === tableName)) {\n\t\t\t\tthrow new Error(`Alias \"${tableName}\" is already used in this query`);\n\t\t\t}\n\n\t\t\tif (typeof on === 'function') {\n\t\t\t\tconst from = this.config.from\n\t\t\t\t\t? is(table, SQLiteTable)\n\t\t\t\t\t\t? table[Table.Symbol.Columns]\n\t\t\t\t\t\t: is(table, Subquery)\n\t\t\t\t\t\t? table._.selectedFields\n\t\t\t\t\t\t: is(table, SQLiteViewBase)\n\t\t\t\t\t\t? table[ViewBaseConfig].selectedFields\n\t\t\t\t\t\t: undefined\n\t\t\t\t\t: undefined;\n\t\t\t\ton = on(\n\t\t\t\t\tnew Proxy(\n\t\t\t\t\t\tthis.config.table[Table.Symbol.Columns],\n\t\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'sql', sqlBehavior: 'sql' }),\n\t\t\t\t\t) as any,\n\t\t\t\t\tfrom && new Proxy(\n\t\t\t\t\t\tfrom,\n\t\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'sql', sqlBehavior: 'sql' }),\n\t\t\t\t\t) as any,\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tthis.config.joins.push({ on, table, joinType, alias: tableName });\n\n\t\t\treturn this as any;\n\t\t}) as any;\n\t}\n\n\tleftJoin = this.createJoin('left');\n\n\trightJoin = this.createJoin('right');\n\n\tinnerJoin = this.createJoin('inner');\n\n\tfullJoin = this.createJoin('full');\n\n\t/**\n\t * Adds a 'where' clause to the query.\n\t *\n\t * Calling this method will update only those rows that fulfill a specified condition.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/update}\n\t *\n\t * @param where the 'where' clause.\n\t *\n\t * @example\n\t * You can use conditional operators and `sql function` to filter the rows to be updated.\n\t *\n\t * ```ts\n\t * // Update all cars with green color\n\t * db.update(cars).set({ color: 'red' })\n\t *   .where(eq(cars.color, 'green'));\n\t * // or\n\t * db.update(cars).set({ color: 'red' })\n\t *   .where(sql`${cars.color} = 'green'`)\n\t * ```\n\t *\n\t * You can logically combine conditional operators with `and()` and `or()` operators:\n\t *\n\t * ```ts\n\t * // Update all BMW cars with a green color\n\t * db.update(cars).set({ color: 'red' })\n\t *   .where(and(eq(cars.color, 'green'), eq(cars.brand, 'BMW')));\n\t *\n\t * // Update all cars with the green or blue color\n\t * db.update(cars).set({ color: 'red' })\n\t *   .where(or(eq(cars.color, 'green'), eq(cars.color, 'blue')));\n\t * ```\n\t */\n\twhere(where: SQL | undefined): SQLiteUpdateWithout<this, TDynamic, 'where'> {\n\t\tthis.config.where = where;\n\t\treturn this as any;\n\t}\n\n\torderBy(\n\t\tbuilder: (updateTable: TTable) => ValueOrArray<SQLiteColumn | SQL | SQL.Aliased>,\n\t): SQLiteUpdateWithout<this, TDynamic, 'orderBy'>;\n\torderBy(...columns: (SQLiteColumn | SQL | SQL.Aliased)[]): SQLiteUpdateWithout<this, TDynamic, 'orderBy'>;\n\torderBy(\n\t\t...columns:\n\t\t\t| [(updateTable: TTable) => ValueOrArray<SQLiteColumn | SQL | SQL.Aliased>]\n\t\t\t| (SQLiteColumn | SQL | SQL.Aliased)[]\n\t): SQLiteUpdateWithout<this, TDynamic, 'orderBy'> {\n\t\tif (typeof columns[0] === 'function') {\n\t\t\tconst orderBy = columns[0](\n\t\t\t\tnew Proxy(\n\t\t\t\t\tthis.config.table[Table.Symbol.Columns],\n\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'alias', sqlBehavior: 'sql' }),\n\t\t\t\t) as any,\n\t\t\t);\n\n\t\t\tconst orderByArray = Array.isArray(orderBy) ? orderBy : [orderBy];\n\t\t\tthis.config.orderBy = orderByArray;\n\t\t} else {\n\t\t\tconst orderByArray = columns as (SQLiteColumn | SQL | SQL.Aliased)[];\n\t\t\tthis.config.orderBy = orderByArray;\n\t\t}\n\t\treturn this as any;\n\t}\n\n\tlimit(limit: number | Placeholder): SQLiteUpdateWithout<this, TDynamic, 'limit'> {\n\t\tthis.config.limit = limit;\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `returning` clause to the query.\n\t *\n\t * Calling this method will return the specified fields of the updated rows. If no fields are specified, all fields will be returned.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/update#update-with-returning}\n\t *\n\t * @example\n\t * ```ts\n\t * // Update all cars with the green color and return all fields\n\t * const updatedCars: Car[] = await db.update(cars)\n\t *   .set({ color: 'red' })\n\t *   .where(eq(cars.color, 'green'))\n\t *   .returning();\n\t *\n\t * // Update all cars with the green color and return only their id and brand fields\n\t * const updatedCarsIdsAndBrands: { id: number, brand: string }[] = await db.update(cars)\n\t *   .set({ color: 'red' })\n\t *   .where(eq(cars.color, 'green'))\n\t *   .returning({ id: cars.id, brand: cars.brand });\n\t * ```\n\t */\n\treturning(): SQLiteUpdateReturningAll<this, TDynamic>;\n\treturning<TSelectedFields extends SelectedFields>(\n\t\tfields: TSelectedFields,\n\t): SQLiteUpdateReturning<this, TDynamic, TSelectedFields>;\n\treturning(\n\t\tfields: SelectedFields = this.config.table[SQLiteTable.Symbol.Columns],\n\t): SQLiteUpdateWithout<AnySQLiteUpdate, TDynamic, 'returning'> {\n\t\tthis.config.returning = orderSelectedFields<SQLiteColumn>(fields);\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildUpdateQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\t/** @internal */\n\t_prepare(isOneTimeQuery = true): SQLiteUpdatePrepare<this> {\n\t\treturn this.session[isOneTimeQuery ? 'prepareOneTimeQuery' : 'prepareQuery'](\n\t\t\tthis.dialect.sqlToQuery(this.getSQL()),\n\t\t\tthis.config.returning,\n\t\t\tthis.config.returning ? 'all' : 'run',\n\t\t\ttrue,\n\t\t\tundefined,\n\t\t\t{\n\t\t\t\ttype: 'insert',\n\t\t\t\ttables: extractUsedTable(this.config.table),\n\t\t\t},\n\t\t) as SQLiteUpdatePrepare<this>;\n\t}\n\n\tprepare(): SQLiteUpdatePrepare<this> {\n\t\treturn this._prepare(false);\n\t}\n\n\trun: ReturnType<this['prepare']>['run'] = (placeholderValues) => {\n\t\treturn this._prepare().run(placeholderValues);\n\t};\n\n\tall: ReturnType<this['prepare']>['all'] = (placeholderValues) => {\n\t\treturn this._prepare().all(placeholderValues);\n\t};\n\n\tget: ReturnType<this['prepare']>['get'] = (placeholderValues) => {\n\t\treturn this._prepare().get(placeholderValues);\n\t};\n\n\tvalues: ReturnType<this['prepare']>['values'] = (placeholderValues) => {\n\t\treturn this._prepare().values(placeholderValues);\n\t};\n\n\toverride async execute(): Promise<SQLiteUpdateExecute<this>> {\n\t\treturn (this.config.returning ? this.all() : this.run()) as SQLiteUpdateExecute<this>;\n\t}\n\n\t$dynamic(): SQLiteUpdateDynamic<this> {\n\t\treturn this as any;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAA+B;AAE/B,2BAA6B;AAE7B,6BAAsC;AAItC,mBAA4B;AAC5B,sBAAyB;AACzB,IAAAA,gBAAsB;AACtB,mBAOO;AACP,yBAA+B;AAE/B,IAAAC,gBAAiC;AACjC,uBAA+B;AAyBxB,MAAM,oBAIX;AAAA,EAOD,YACW,OACA,SACA,SACF,UACP;AAJS;AACA;AACA;AACF;AAAA,EACN;AAAA,EAXH,QAAiB,wBAAU,IAAY;AAAA,EAavC,IACC,QAKC;AACD,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,UACL,2BAAa,KAAK,OAAO,MAAM;AAAA,MAC/B,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACN;AAAA,EACD;AACD;AA+IO,MAAM,yBAWH,kCAEV;AAAA,EAMC,YACC,OACA,KACQ,SACA,SACR,UACC;AACD,UAAM;AAJE;AACA;AAIR,SAAK,SAAS,EAAE,KAAK,OAAO,UAAU,OAAO,CAAC,EAAE;AAAA,EACjD;AAAA,EAdA,QAA0B,wBAAU,IAAY;AAAA;AAAA,EAGhD;AAAA,EAaA,KACC,QAC+C;AAC/C,SAAK,OAAO,OAAO;AACnB,WAAO;AAAA,EACR;AAAA,EAEQ,WACP,UAC2B;AAC3B,WAAQ,CACP,OACA,OACI;AACJ,YAAM,gBAAY,+BAAiB,KAAK;AAExC,UAAI,OAAO,cAAc,YAAY,KAAK,OAAO,MAAM,KAAK,CAAC,SAAS,KAAK,UAAU,SAAS,GAAG;AAChG,cAAM,IAAI,MAAM,UAAU,SAAS,iCAAiC;AAAA,MACrE;AAEA,UAAI,OAAO,OAAO,YAAY;AAC7B,cAAM,OAAO,KAAK,OAAO,WACtB,kBAAG,OAAO,wBAAW,IACpB,MAAM,oBAAM,OAAO,OAAO,QAC1B,kBAAG,OAAO,wBAAQ,IAClB,MAAM,EAAE,qBACR,kBAAG,OAAO,+BAAc,IACxB,MAAM,iCAAc,EAAE,iBACtB,SACD;AACH,aAAK;AAAA,UACJ,IAAI;AAAA,YACH,KAAK,OAAO,MAAM,oBAAM,OAAO,OAAO;AAAA,YACtC,IAAI,6CAAsB,EAAE,oBAAoB,OAAO,aAAa,MAAM,CAAC;AAAA,UAC5E;AAAA,UACA,QAAQ,IAAI;AAAA,YACX;AAAA,YACA,IAAI,6CAAsB,EAAE,oBAAoB,OAAO,aAAa,MAAM,CAAC;AAAA,UAC5E;AAAA,QACD;AAAA,MACD;AAEA,WAAK,OAAO,MAAM,KAAK,EAAE,IAAI,OAAO,UAAU,OAAO,UAAU,CAAC;AAEhE,aAAO;AAAA,IACR;AAAA,EACD;AAAA,EAEA,WAAW,KAAK,WAAW,MAAM;AAAA,EAEjC,YAAY,KAAK,WAAW,OAAO;AAAA,EAEnC,YAAY,KAAK,WAAW,OAAO;AAAA,EAEnC,WAAW,KAAK,WAAW,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmCjC,MAAM,OAAsE;AAC3E,SAAK,OAAO,QAAQ;AACpB,WAAO;AAAA,EACR;AAAA,EAMA,WACI,SAG8C;AACjD,QAAI,OAAO,QAAQ,CAAC,MAAM,YAAY;AACrC,YAAM,UAAU,QAAQ,CAAC;AAAA,QACxB,IAAI;AAAA,UACH,KAAK,OAAO,MAAM,oBAAM,OAAO,OAAO;AAAA,UACtC,IAAI,6CAAsB,EAAE,oBAAoB,SAAS,aAAa,MAAM,CAAC;AAAA,QAC9E;AAAA,MACD;AAEA,YAAM,eAAe,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AAChE,WAAK,OAAO,UAAU;AAAA,IACvB,OAAO;AACN,YAAM,eAAe;AACrB,WAAK,OAAO,UAAU;AAAA,IACvB;AACA,WAAO;AAAA,EACR;AAAA,EAEA,MAAM,OAA2E;AAChF,SAAK,OAAO,QAAQ;AACpB,WAAO;AAAA,EACR;AAAA,EA4BA,UACC,SAAyB,KAAK,OAAO,MAAM,yBAAY,OAAO,OAAO,GACP;AAC9D,SAAK,OAAO,gBAAY,kCAAkC,MAAM;AAChE,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,iBAAiB,KAAK,MAAM;AAAA,EACjD;AAAA,EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAS,iBAAiB,MAAiC;AAC1D,WAAO,KAAK,QAAQ,iBAAiB,wBAAwB,cAAc;AAAA,MAC1E,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAAA,MACrC,KAAK,OAAO;AAAA,MACZ,KAAK,OAAO,YAAY,QAAQ;AAAA,MAChC;AAAA,MACA;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,YAAQ,gCAAiB,KAAK,OAAO,KAAK;AAAA,MAC3C;AAAA,IACD;AAAA,EACD;AAAA,EAEA,UAAqC;AACpC,WAAO,KAAK,SAAS,KAAK;AAAA,EAC3B;AAAA,EAEA,MAA0C,CAAC,sBAAsB;AAChE,WAAO,KAAK,SAAS,EAAE,IAAI,iBAAiB;AAAA,EAC7C;AAAA,EAEA,MAA0C,CAAC,sBAAsB;AAChE,WAAO,KAAK,SAAS,EAAE,IAAI,iBAAiB;AAAA,EAC7C;AAAA,EAEA,MAA0C,CAAC,sBAAsB;AAChE,WAAO,KAAK,SAAS,EAAE,IAAI,iBAAiB;AAAA,EAC7C;AAAA,EAEA,SAAgD,CAAC,sBAAsB;AACtE,WAAO,KAAK,SAAS,EAAE,OAAO,iBAAiB;AAAA,EAChD;AAAA,EAEA,MAAe,UAA8C;AAC5D,WAAQ,KAAK,OAAO,YAAY,KAAK,IAAI,IAAI,KAAK,IAAI;AAAA,EACvD;AAAA,EAEA,WAAsC;AACrC,WAAO;AAAA,EACR;AACD;", "names": ["import_table", "import_utils"]}
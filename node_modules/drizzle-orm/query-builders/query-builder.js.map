{"version": 3, "sources": ["../../src/query-builders/query-builder.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { SQL, SQLWrapper } from '~/sql/index.ts';\n\nexport abstract class TypedQueryBuilder<TSelection, TResult = unknown, TConfig = unknown> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'TypedQueryBuilder';\n\n\tdeclare _: {\n\t\tselectedFields: TSelection;\n\t\tresult: TResult;\n\t\tconfig?: TConfig;\n\t};\n\n\t/** @internal */\n\tgetSelectedFields(): TSelection {\n\t\treturn this._.selectedFields;\n\t}\n\n\tabstract getSQL(): SQL;\n}\n"], "mappings": "AAAA,SAAS,kBAAkB;AAGpB,MAAe,kBAA0F;AAAA,EAC/G,QAAiB,UAAU,IAAY;AAAA;AAAA,EASvC,oBAAgC;AAC/B,WAAO,KAAK,EAAE;AAAA,EACf;AAGD;", "names": []}
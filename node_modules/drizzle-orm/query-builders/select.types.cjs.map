{"version": 3, "sources": ["../../src/query-builders/select.types.ts"], "sourcesContent": ["import type { ChangeColumnTableName, ColumnDataType, Dialect } from '~/column-builder.ts';\nimport type { AnyColumn, Column, ColumnBaseConfig, GetColumnData, UpdateColConfig } from '~/column.ts';\nimport type { SelectedFields } from '~/operations.ts';\nimport type { ColumnsSelection, SQL, View } from '~/sql/sql.ts';\nimport type { Subquery } from '~/subquery.ts';\nimport type { Table } from '~/table.ts';\nimport type { Assume, DrizzleTypeError, Equal, IsAny, Simplify } from '~/utils.ts';\n\nexport type JoinType = 'inner' | 'left' | 'right' | 'full' | 'cross';\n\nexport type JoinNullability = 'nullable' | 'not-null';\n\nexport type ApplyNullability<T, TNullability extends JoinNullability> = TNullability extends 'nullable' ? T | null\n\t: TNullability extends 'null' ? null\n\t: T;\n\nexport type ApplyNullabilityToColumn<TColumn extends Column, TNullability extends JoinNullability> =\n\tTNullability extends 'not-null' ? TColumn\n\t\t: Column<\n\t\t\tAssume<\n\t\t\t\tUpdateColConfig<TColumn['_'], {\n\t\t\t\t\tnotNull: TNullability extends 'nullable' ? false : TColumn['_']['notNull'];\n\t\t\t\t}>,\n\t\t\t\tColumnBaseConfig<ColumnDataType, string>\n\t\t\t>\n\t\t>;\n\nexport type ApplyNotNullMapToJoins<TResult, TNullabilityMap extends Record<string, JoinNullability>> =\n\t& {\n\t\t[TTableName in keyof TResult & keyof TNullabilityMap & string]: ApplyNullability<\n\t\t\tTResult[TTableName],\n\t\t\tTNullabilityMap[TTableName]\n\t\t>;\n\t}\n\t& {};\n\nexport type SelectMode = 'partial' | 'single' | 'multiple';\n\nexport type SelectResult<\n\tTResult,\n\tTSelectMode extends SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability>,\n> = TSelectMode extends 'partial' ? SelectPartialResult<TResult, TNullabilityMap>\n\t: TSelectMode extends 'single' ? SelectResultFields<TResult>\n\t: ApplyNotNullMapToJoins<SelectResultFields<TResult>, TNullabilityMap>;\n\ntype IsUnion<T, U extends T = T> = (T extends any ? (U extends T ? false : true) : never) extends false ? false : true;\n\ntype Not<T extends boolean> = T extends true ? false : true;\n\ntype SelectPartialResult<TFields, TNullability extends Record<string, JoinNullability>> = TNullability extends\n\tTNullability ? {\n\t\t[Key in keyof TFields]: TFields[Key] extends infer TField\n\t\t\t? TField extends Table ? TField['_']['name'] extends keyof TNullability ? ApplyNullability<\n\t\t\t\t\t\tSelectResultFields<TField['_']['columns']>,\n\t\t\t\t\t\tTNullability[TField['_']['name']]\n\t\t\t\t\t>\n\t\t\t\t: never\n\t\t\t: TField extends Column\n\t\t\t\t? TField['_']['tableName'] extends keyof TNullability\n\t\t\t\t\t? ApplyNullability<SelectResultField<TField>, TNullability[TField['_']['tableName']]>\n\t\t\t\t: never\n\t\t\t: TField extends SQL | SQL.Aliased ? SelectResultField<TField>\n\t\t\t: TField extends Record<string, any>\n\t\t\t\t? TField[keyof TField] extends AnyColumn<{ tableName: infer TTableName extends string }> | SQL | SQL.Aliased\n\t\t\t\t\t? Not<IsUnion<TTableName>> extends true\n\t\t\t\t\t\t? ApplyNullability<SelectResultFields<TField>, TNullability[TTableName]>\n\t\t\t\t\t: SelectPartialResult<TField, TNullability>\n\t\t\t\t: never\n\t\t\t: never\n\t\t\t: never;\n\t}\n\t: never;\n\nexport type MapColumnsToTableAlias<\n\tTColumns extends ColumnsSelection,\n\tTAlias extends string,\n\tTDialect extends Dialect,\n> =\n\t& {\n\t\t[Key in keyof TColumns]: TColumns[Key] extends Column\n\t\t\t? ChangeColumnTableName<Assume<TColumns[Key], Column>, TAlias, TDialect>\n\t\t\t: TColumns[Key];\n\t}\n\t& {};\n\nexport type AddAliasToSelection<\n\tTSelection extends ColumnsSelection,\n\tTAlias extends string,\n\tTDialect extends Dialect,\n> = Simplify<\n\tIsAny<TSelection> extends true ? any\n\t\t: {\n\t\t\t[Key in keyof TSelection]: TSelection[Key] extends Column\n\t\t\t\t? ChangeColumnTableName<TSelection[Key], TAlias, TDialect>\n\t\t\t\t: TSelection[Key] extends Table ? AddAliasToSelection<TSelection[Key]['_']['columns'], TAlias, TDialect>\n\t\t\t\t: TSelection[Key] extends SQL | SQL.Aliased ? TSelection[Key]\n\t\t\t\t: TSelection[Key] extends ColumnsSelection ? MapColumnsToTableAlias<TSelection[Key], TAlias, TDialect>\n\t\t\t\t: never;\n\t\t}\n>;\n\nexport type AppendToResult<\n\tTTableName extends string | undefined,\n\tTResult,\n\tTJoinedName extends string | undefined,\n\tTSelectedFields extends SelectedFields<Column, Table>,\n\tTOldSelectMode extends SelectMode,\n> = TOldSelectMode extends 'partial' ? TResult\n\t: TOldSelectMode extends 'single' ?\n\t\t\t& (TTableName extends string ? Record<TTableName, TResult> : TResult)\n\t\t\t& (TJoinedName extends string ? Record<TJoinedName, TSelectedFields> : TSelectedFields)\n\t: TResult & (TJoinedName extends string ? Record<TJoinedName, TSelectedFields> : TSelectedFields);\n\nexport type BuildSubquerySelection<\n\tTSelection extends ColumnsSelection,\n\tTNullability extends Record<string, JoinNullability>,\n> = TSelection extends never ? any\n\t:\n\t\t& {\n\t\t\t[Key in keyof TSelection]: TSelection[Key] extends SQL\n\t\t\t\t? DrizzleTypeError<'You cannot reference this field without assigning it an alias first - use `.as(<alias>)`'>\n\t\t\t\t: TSelection[Key] extends SQL.Aliased ? TSelection[Key]\n\t\t\t\t: TSelection[Key] extends Table ? BuildSubquerySelection<TSelection[Key]['_']['columns'], TNullability>\n\t\t\t\t: TSelection[Key] extends Column\n\t\t\t\t\t? ApplyNullabilityToColumn<TSelection[Key], TNullability[TSelection[Key]['_']['tableName']]>\n\t\t\t\t: TSelection[Key] extends ColumnsSelection ? BuildSubquerySelection<TSelection[Key], TNullability>\n\t\t\t\t: never;\n\t\t}\n\t\t& {};\n\ntype SetJoinsNullability<TNullabilityMap extends Record<string, JoinNullability>, TValue extends JoinNullability> = {\n\t[Key in keyof TNullabilityMap]: TValue;\n};\n\nexport type AppendToNullabilityMap<\n\tTJoinsNotNull extends Record<string, JoinNullability>,\n\tTJoinedName extends string | undefined,\n\tTJoinType extends JoinType,\n> = TJoinedName extends string ? 'left' extends TJoinType ? TJoinsNotNull & { [name in TJoinedName]: 'nullable' }\n\t: 'right' extends TJoinType ? SetJoinsNullability<TJoinsNotNull, 'nullable'> & { [name in TJoinedName]: 'not-null' }\n\t: 'inner' extends TJoinType ? TJoinsNotNull & { [name in TJoinedName]: 'not-null' }\n\t: 'cross' extends TJoinType ? TJoinsNotNull & { [name in TJoinedName]: 'not-null' }\n\t: 'full' extends TJoinType ? SetJoinsNullability<TJoinsNotNull, 'nullable'> & { [name in TJoinedName]: 'nullable' }\n\t: never\n\t: TJoinsNotNull;\n\nexport type TableLike = Table | Subquery | View | SQL;\n\nexport type GetSelectTableName<TTable extends TableLike> = TTable extends Table ? TTable['_']['name']\n\t: TTable extends Subquery ? TTable['_']['alias']\n\t: TTable extends View ? TTable['_']['name']\n\t: TTable extends SQL ? undefined\n\t: never;\n\nexport type GetSelectTableSelection<TTable extends TableLike> = TTable extends Table ? TTable['_']['columns']\n\t: TTable extends Subquery | View ? Assume<TTable['_']['selectedFields'], ColumnsSelection>\n\t: TTable extends SQL ? {}\n\t: never;\n\nexport type SelectResultField<T, TDeep extends boolean = true> = T extends DrizzleTypeError<any> ? T\n\t: T extends Table ? Equal<TDeep, true> extends true ? SelectResultField<T['_']['columns'], false> : never\n\t: T extends Column<any> ? GetColumnData<T>\n\t: T extends SQL | SQL.Aliased ? T['_']['type']\n\t: T extends Record<string, any> ? SelectResultFields<T, true>\n\t: never;\n\nexport type SelectResultFields<TSelectedFields, TDeep extends boolean = true> = Simplify<\n\t{\n\t\t[Key in keyof TSelectedFields]: SelectResultField<TSelectedFields[Key], TDeep>;\n\t}\n>;\n\nexport type SetOperator = 'union' | 'intersect' | 'except';\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}
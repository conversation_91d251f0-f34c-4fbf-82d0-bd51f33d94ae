{"version": 3, "sources": ["../../src/singlestore-core/utils.ts"], "sourcesContent": ["import { is } from '~/entity.ts';\nimport { SQL } from '~/sql/sql.ts';\nimport { Subquery } from '~/subquery.ts';\nimport { Table } from '~/table.ts';\nimport type { Index } from './indexes.ts';\nimport { IndexBuilder } from './indexes.ts';\nimport type { PrimaryKey } from './primary-keys.ts';\nimport { PrimaryKeyBuilder } from './primary-keys.ts';\nimport { SingleStoreTable } from './table.ts';\nimport { type UniqueConstraint, UniqueConstraintBuilder } from './unique-constraint.ts';\n/* import { SingleStoreViewConfig } from './view-common.ts';\nimport type { SingleStoreView } from './view.ts'; */\n\nexport function extractUsedTable(table: SingleStoreTable | Subquery | SQL): string[] {\n\tif (is(table, SingleStoreTable)) {\n\t\treturn [`${table[Table.Symbol.BaseName]}`];\n\t}\n\tif (is(table, Subquery)) {\n\t\treturn table._.usedTables ?? [];\n\t}\n\tif (is(table, SQL)) {\n\t\treturn table.usedTables ?? [];\n\t}\n\treturn [];\n}\n\nexport function getTableConfig(table: SingleStoreTable) {\n\tconst columns = Object.values(table[SingleStoreTable.Symbol.Columns]);\n\tconst indexes: Index[] = [];\n\tconst primaryKeys: PrimaryKey[] = [];\n\tconst uniqueConstraints: UniqueConstraint[] = [];\n\tconst name = table[Table.Symbol.Name];\n\tconst schema = table[Table.Symbol.Schema];\n\tconst baseName = table[Table.Symbol.BaseName];\n\n\tconst extraConfigBuilder = table[SingleStoreTable.Symbol.ExtraConfigBuilder];\n\n\tif (extraConfigBuilder !== undefined) {\n\t\tconst extraConfig = extraConfigBuilder(table[SingleStoreTable.Symbol.Columns]);\n\t\tconst extraValues = Array.isArray(extraConfig) ? extraConfig.flat(1) as any[] : Object.values(extraConfig);\n\t\tfor (const builder of Object.values(extraValues)) {\n\t\t\tif (is(builder, IndexBuilder)) {\n\t\t\t\tindexes.push(builder.build(table));\n\t\t\t} else if (is(builder, UniqueConstraintBuilder)) {\n\t\t\t\tuniqueConstraints.push(builder.build(table));\n\t\t\t} else if (is(builder, PrimaryKeyBuilder)) {\n\t\t\t\tprimaryKeys.push(builder.build(table));\n\t\t\t}\n\t\t}\n\t}\n\n\treturn {\n\t\tcolumns,\n\t\tindexes,\n\t\tprimaryKeys,\n\t\tuniqueConstraints,\n\t\tname,\n\t\tschema,\n\t\tbaseName,\n\t};\n}\n\n/* export function getViewConfig<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n>(view: SingleStoreView<TName, TExisting>) {\n\treturn {\n\t\t...view[ViewBaseConfig],\n\t\t...view[SingleStoreViewConfig],\n\t};\n} */\n"], "mappings": "AAAA,SAAS,UAAU;AACnB,SAAS,WAAW;AACpB,SAAS,gBAAgB;AACzB,SAAS,aAAa;AAEtB,SAAS,oBAAoB;AAE7B,SAAS,yBAAyB;AAClC,SAAS,wBAAwB;AACjC,SAAgC,+BAA+B;AAIxD,SAAS,iBAAiB,OAAoD;AACpF,MAAI,GAAG,OAAO,gBAAgB,GAAG;AAChC,WAAO,CAAC,GAAG,MAAM,MAAM,OAAO,QAAQ,CAAC,EAAE;AAAA,EAC1C;AACA,MAAI,GAAG,OAAO,QAAQ,GAAG;AACxB,WAAO,MAAM,EAAE,cAAc,CAAC;AAAA,EAC/B;AACA,MAAI,GAAG,OAAO,GAAG,GAAG;AACnB,WAAO,MAAM,cAAc,CAAC;AAAA,EAC7B;AACA,SAAO,CAAC;AACT;AAEO,SAAS,eAAe,OAAyB;AACvD,QAAM,UAAU,OAAO,OAAO,MAAM,iBAAiB,OAAO,OAAO,CAAC;AACpE,QAAM,UAAmB,CAAC;AAC1B,QAAM,cAA4B,CAAC;AACnC,QAAM,oBAAwC,CAAC;AAC/C,QAAM,OAAO,MAAM,MAAM,OAAO,IAAI;AACpC,QAAM,SAAS,MAAM,MAAM,OAAO,MAAM;AACxC,QAAM,WAAW,MAAM,MAAM,OAAO,QAAQ;AAE5C,QAAM,qBAAqB,MAAM,iBAAiB,OAAO,kBAAkB;AAE3E,MAAI,uBAAuB,QAAW;AACrC,UAAM,cAAc,mBAAmB,MAAM,iBAAiB,OAAO,OAAO,CAAC;AAC7E,UAAM,cAAc,MAAM,QAAQ,WAAW,IAAI,YAAY,KAAK,CAAC,IAAa,OAAO,OAAO,WAAW;AACzG,eAAW,WAAW,OAAO,OAAO,WAAW,GAAG;AACjD,UAAI,GAAG,SAAS,YAAY,GAAG;AAC9B,gBAAQ,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA,MAClC,WAAW,GAAG,SAAS,uBAAuB,GAAG;AAChD,0BAAkB,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA,MAC5C,WAAW,GAAG,SAAS,iBAAiB,GAAG;AAC1C,oBAAY,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA,MACtC;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACD;", "names": []}
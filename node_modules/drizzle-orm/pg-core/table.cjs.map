{"version": 3, "sources": ["../../src/pg-core/table.ts"], "sourcesContent": ["import type { BuildColumns, BuildExtraConfigColumns } from '~/column-builder.ts';\nimport { entityKind } from '~/entity.ts';\nimport { Table, type TableConfig as TableConfigBase, type UpdateTableConfig } from '~/table.ts';\nimport type { CheckBuilder } from './checks.ts';\nimport { getPgColumnBuilders, type PgColumnsBuilders } from './columns/all.ts';\nimport type { ExtraConfigColumn, PgColumn, PgColumnBuilder, PgColumnBuilderBase } from './columns/common.ts';\nimport type { ForeignKey, ForeignKeyBuilder } from './foreign-keys.ts';\nimport type { AnyIndexBuilder } from './indexes.ts';\nimport type { PgPolicy } from './policies.ts';\nimport type { PrimaryKeyBuilder } from './primary-keys.ts';\nimport type { UniqueConstraintBuilder } from './unique-constraint.ts';\n\nexport type PgTableExtraConfigValue =\n\t| AnyIndexBuilder\n\t| CheckBuilder\n\t| ForeignKeyBuilder\n\t| PrimaryKeyBuilder\n\t| UniqueConstraintBuilder\n\t| PgPolicy;\n\nexport type PgTableExtraConfig = Record<\n\tstring,\n\tPgTableExtraConfigValue\n>;\n\nexport type TableConfig = TableConfigBase<PgColumn>;\n\n/** @internal */\nexport const InlineForeignKeys = Symbol.for('drizzle:PgInlineForeignKeys');\n/** @internal */\nexport const EnableRLS = Symbol.for('drizzle:EnableRLS');\n\nexport class PgTable<T extends TableConfig = TableConfig> extends Table<T> {\n\tstatic override readonly [entityKind]: string = 'PgTable';\n\n\t/** @internal */\n\tstatic override readonly Symbol = Object.assign({}, Table.Symbol, {\n\t\tInlineForeignKeys: InlineForeignKeys as typeof InlineForeignKeys,\n\t\tEnableRLS: EnableRLS as typeof EnableRLS,\n\t});\n\n\t/**@internal */\n\t[InlineForeignKeys]: ForeignKey[] = [];\n\n\t/** @internal */\n\t[EnableRLS]: boolean = false;\n\n\t/** @internal */\n\toverride [Table.Symbol.ExtraConfigBuilder]: ((self: Record<string, PgColumn>) => PgTableExtraConfig) | undefined =\n\t\tundefined;\n\n\t/** @internal */\n\toverride [Table.Symbol.ExtraConfigColumns]: Record<string, ExtraConfigColumn> = {};\n}\n\nexport type AnyPgTable<TPartial extends Partial<TableConfig> = {}> = PgTable<UpdateTableConfig<TableConfig, TPartial>>;\n\nexport type PgTableWithColumns<T extends TableConfig> =\n\t& PgTable<T>\n\t& {\n\t\t[Key in keyof T['columns']]: T['columns'][Key];\n\t}\n\t& {\n\t\tenableRLS: () => Omit<\n\t\t\tPgTableWithColumns<T>,\n\t\t\t'enableRLS'\n\t\t>;\n\t};\n\n/** @internal */\nexport function pgTableWithSchema<\n\tTTableName extends string,\n\tTSchemaName extends string | undefined,\n\tTColumnsMap extends Record<string, PgColumnBuilderBase>,\n>(\n\tname: TTableName,\n\tcolumns: TColumnsMap | ((columnTypes: PgColumnsBuilders) => TColumnsMap),\n\textraConfig:\n\t\t| ((self: BuildExtraConfigColumns<TTableName, TColumnsMap, 'pg'>) => PgTableExtraConfig | PgTableExtraConfigValue[])\n\t\t| undefined,\n\tschema: TSchemaName,\n\tbaseName = name,\n): PgTableWithColumns<{\n\tname: TTableName;\n\tschema: TSchemaName;\n\tcolumns: BuildColumns<TTableName, TColumnsMap, 'pg'>;\n\tdialect: 'pg';\n}> {\n\tconst rawTable = new PgTable<{\n\t\tname: TTableName;\n\t\tschema: TSchemaName;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'pg'>;\n\t\tdialect: 'pg';\n\t}>(name, schema, baseName);\n\n\tconst parsedColumns: TColumnsMap = typeof columns === 'function' ? columns(getPgColumnBuilders()) : columns;\n\n\tconst builtColumns = Object.fromEntries(\n\t\tObject.entries(parsedColumns).map(([name, colBuilderBase]) => {\n\t\t\tconst colBuilder = colBuilderBase as PgColumnBuilder;\n\t\t\tcolBuilder.setName(name);\n\t\t\tconst column = colBuilder.build(rawTable);\n\t\t\trawTable[InlineForeignKeys].push(...colBuilder.buildForeignKeys(column, rawTable));\n\t\t\treturn [name, column];\n\t\t}),\n\t) as unknown as BuildColumns<TTableName, TColumnsMap, 'pg'>;\n\n\tconst builtColumnsForExtraConfig = Object.fromEntries(\n\t\tObject.entries(parsedColumns).map(([name, colBuilderBase]) => {\n\t\t\tconst colBuilder = colBuilderBase as PgColumnBuilder;\n\t\t\tcolBuilder.setName(name);\n\t\t\tconst column = colBuilder.buildExtraConfigColumn(rawTable);\n\t\t\treturn [name, column];\n\t\t}),\n\t) as unknown as BuildExtraConfigColumns<TTableName, TColumnsMap, 'pg'>;\n\n\tconst table = Object.assign(rawTable, builtColumns);\n\n\ttable[Table.Symbol.Columns] = builtColumns;\n\ttable[Table.Symbol.ExtraConfigColumns] = builtColumnsForExtraConfig;\n\n\tif (extraConfig) {\n\t\ttable[PgTable.Symbol.ExtraConfigBuilder] = extraConfig as any;\n\t}\n\n\treturn Object.assign(table, {\n\t\tenableRLS: () => {\n\t\t\ttable[PgTable.Symbol.EnableRLS] = true;\n\t\t\treturn table as PgTableWithColumns<{\n\t\t\t\tname: TTableName;\n\t\t\t\tschema: TSchemaName;\n\t\t\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'pg'>;\n\t\t\t\tdialect: 'pg';\n\t\t\t}>;\n\t\t},\n\t});\n}\n\nexport interface PgTableFn<TSchema extends string | undefined = undefined> {\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, PgColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: TColumnsMap,\n\t\textraConfig?: (\n\t\t\tself: BuildExtraConfigColumns<TTableName, TColumnsMap, 'pg'>,\n\t\t) => PgTableExtraConfigValue[],\n\t): PgTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchema;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'pg'>;\n\t\tdialect: 'pg';\n\t}>;\n\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, PgColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: (columnTypes: PgColumnsBuilders) => TColumnsMap,\n\t\textraConfig?: (self: BuildExtraConfigColumns<TTableName, TColumnsMap, 'pg'>) => PgTableExtraConfigValue[],\n\t): PgTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchema;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'pg'>;\n\t\tdialect: 'pg';\n\t}>;\n\t/**\n\t * @deprecated The third parameter of pgTable is changing and will only accept an array instead of an object\n\t *\n\t * @example\n\t * Deprecated version:\n\t * ```ts\n\t * export const users = pgTable(\"users\", {\n\t * \tid: integer(),\n\t * }, (t) => ({\n\t * \tidx: index('custom_name').on(t.id)\n\t * }));\n\t * ```\n\t *\n\t * New API:\n\t * ```ts\n\t * export const users = pgTable(\"users\", {\n\t * \tid: integer(),\n\t * }, (t) => [\n\t * \tindex('custom_name').on(t.id)\n\t * ]);\n\t * ```\n\t */\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, PgColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: TColumnsMap,\n\t\textraConfig: (\n\t\t\tself: BuildExtraConfigColumns<TTableName, TColumnsMap, 'pg'>,\n\t\t) => PgTableExtraConfig,\n\t): PgTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchema;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'pg'>;\n\t\tdialect: 'pg';\n\t}>;\n\n\t/**\n\t * @deprecated The third parameter of pgTable is changing and will only accept an array instead of an object\n\t *\n\t * @example\n\t * Deprecated version:\n\t * ```ts\n\t * export const users = pgTable(\"users\", {\n\t * \tid: integer(),\n\t * }, (t) => ({\n\t * \tidx: index('custom_name').on(t.id)\n\t * }));\n\t * ```\n\t *\n\t * New API:\n\t * ```ts\n\t * export const users = pgTable(\"users\", {\n\t * \tid: integer(),\n\t * }, (t) => [\n\t * \tindex('custom_name').on(t.id)\n\t * ]);\n\t * ```\n\t */\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, PgColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: (columnTypes: PgColumnsBuilders) => TColumnsMap,\n\t\textraConfig: (self: BuildExtraConfigColumns<TTableName, TColumnsMap, 'pg'>) => PgTableExtraConfig,\n\t): PgTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchema;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'pg'>;\n\t\tdialect: 'pg';\n\t}>;\n}\n\nexport const pgTable: PgTableFn = (name, columns, extraConfig) => {\n\treturn pgTableWithSchema(name, columns, extraConfig, undefined);\n};\n\nexport function pgTableCreator(customizeTableName: (name: string) => string): PgTableFn {\n\treturn (name, columns, extraConfig) => {\n\t\treturn pgTableWithSchema(customizeTableName(name) as typeof name, columns, extraConfig, undefined, name);\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAA2B;AAC3B,mBAAmF;AAEnF,iBAA4D;AAwBrD,MAAM,oBAAoB,OAAO,IAAI,6BAA6B;AAElE,MAAM,YAAY,OAAO,IAAI,mBAAmB;AAEhD,MAAM,gBAAqD,mBAAS;AAAA,EAC1E,QAA0B,wBAAU,IAAY;AAAA;AAAA,EAGhD,OAAyB,SAAS,OAAO,OAAO,CAAC,GAAG,mBAAM,QAAQ;AAAA,IACjE;AAAA,IACA;AAAA,EACD,CAAC;AAAA;AAAA,EAGD,CAAC,iBAAiB,IAAkB,CAAC;AAAA;AAAA,EAGrC,CAAC,SAAS,IAAa;AAAA;AAAA,EAGvB,CAAU,mBAAM,OAAO,kBAAkB,IACxC;AAAA;AAAA,EAGD,CAAU,mBAAM,OAAO,kBAAkB,IAAuC,CAAC;AAClF;AAiBO,SAAS,kBAKf,MACA,SACA,aAGA,QACA,WAAW,MAMT;AACF,QAAM,WAAW,IAAI,QAKlB,MAAM,QAAQ,QAAQ;AAEzB,QAAM,gBAA6B,OAAO,YAAY,aAAa,YAAQ,gCAAoB,CAAC,IAAI;AAEpG,QAAM,eAAe,OAAO;AAAA,IAC3B,OAAO,QAAQ,aAAa,EAAE,IAAI,CAAC,CAACA,OAAM,cAAc,MAAM;AAC7D,YAAM,aAAa;AACnB,iBAAW,QAAQA,KAAI;AACvB,YAAM,SAAS,WAAW,MAAM,QAAQ;AACxC,eAAS,iBAAiB,EAAE,KAAK,GAAG,WAAW,iBAAiB,QAAQ,QAAQ,CAAC;AACjF,aAAO,CAACA,OAAM,MAAM;AAAA,IACrB,CAAC;AAAA,EACF;AAEA,QAAM,6BAA6B,OAAO;AAAA,IACzC,OAAO,QAAQ,aAAa,EAAE,IAAI,CAAC,CAACA,OAAM,cAAc,MAAM;AAC7D,YAAM,aAAa;AACnB,iBAAW,QAAQA,KAAI;AACvB,YAAM,SAAS,WAAW,uBAAuB,QAAQ;AACzD,aAAO,CAACA,OAAM,MAAM;AAAA,IACrB,CAAC;AAAA,EACF;AAEA,QAAM,QAAQ,OAAO,OAAO,UAAU,YAAY;AAElD,QAAM,mBAAM,OAAO,OAAO,IAAI;AAC9B,QAAM,mBAAM,OAAO,kBAAkB,IAAI;AAEzC,MAAI,aAAa;AAChB,UAAM,QAAQ,OAAO,kBAAkB,IAAI;AAAA,EAC5C;AAEA,SAAO,OAAO,OAAO,OAAO;AAAA,IAC3B,WAAW,MAAM;AAChB,YAAM,QAAQ,OAAO,SAAS,IAAI;AAClC,aAAO;AAAA,IAMR;AAAA,EACD,CAAC;AACF;AA2GO,MAAM,UAAqB,CAAC,MAAM,SAAS,gBAAgB;AACjE,SAAO,kBAAkB,MAAM,SAAS,aAAa,MAAS;AAC/D;AAEO,SAAS,eAAe,oBAAyD;AACvF,SAAO,CAAC,MAAM,SAAS,gBAAgB;AACtC,WAAO,kBAAkB,mBAAmB,IAAI,GAAkB,SAAS,aAAa,QAAW,IAAI;AAAA,EACxG;AACD;", "names": ["name"]}
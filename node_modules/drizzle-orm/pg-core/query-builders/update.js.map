{"version": 3, "sources": ["../../../src/pg-core/query-builders/update.ts"], "sourcesContent": ["import type { WithCacheConfig } from '~/cache/core/types.ts';\nimport type { GetColumnData } from '~/column.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport type {\n\tPgPreparedQuery,\n\tPgQueryResultHKT,\n\tPgQueryResultKind,\n\tPgSession,\n\tPreparedQueryConfig,\n} from '~/pg-core/session.ts';\nimport { PgTable } from '~/pg-core/table.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type {\n\tAppendToNullabilityMap,\n\tAppendToResult,\n\tGetSelectTableName,\n\tGetSelectTableSelection,\n\tJoinNullability,\n\tJoinType,\n\tSelectMode,\n\tSelectResult,\n} from '~/query-builders/select.types.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport { type ColumnsSelection, type Query, SQL, type SQLWrapper } from '~/sql/sql.ts';\nimport { Subquery } from '~/subquery.ts';\nimport { getTableName, Table } from '~/table.ts';\nimport {\n\ttype Assume,\n\ttype DrizzleTypeError,\n\ttype Equal,\n\tgetTableLikeName,\n\tmapUpdateSet,\n\ttype NeonAuthToken,\n\torderSelectedFields,\n\ttype Simplify,\n\ttype UpdateSet,\n} from '~/utils.ts';\nimport { ViewBaseConfig } from '~/view-common.ts';\nimport type { PgColumn } from '../columns/common.ts';\nimport { extractUsedTable } from '../utils.ts';\nimport type { PgViewBase } from '../view-base.ts';\nimport type {\n\tPgSelectJoinConfig,\n\tSelectedFields,\n\tSelectedFieldsOrdered,\n\tTableLikeHasEmptySelection,\n} from './select.types.ts';\n\nexport interface PgUpdateConfig {\n\twhere?: SQL | undefined;\n\tset: UpdateSet;\n\ttable: PgTable;\n\tfrom?: PgTable | Subquery | PgViewBase | SQL;\n\tjoins: PgSelectJoinConfig[];\n\treturningFields?: SelectedFields;\n\treturning?: SelectedFieldsOrdered;\n\twithList?: Subquery[];\n}\n\nexport type PgUpdateSetSource<TTable extends PgTable> =\n\t& {\n\t\t[Key in keyof TTable['$inferInsert']]?:\n\t\t\t| GetColumnData<TTable['_']['columns'][Key]>\n\t\t\t| SQL\n\t\t\t| PgColumn\n\t\t\t| undefined;\n\t}\n\t& {};\n\nexport class PgUpdateBuilder<TTable extends PgTable, TQueryResult extends PgQueryResultHKT> {\n\tstatic readonly [entityKind]: string = 'PgUpdateBuilder';\n\n\tdeclare readonly _: {\n\t\treadonly table: TTable;\n\t};\n\n\tconstructor(\n\t\tprivate table: TTable,\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t\tprivate withList?: Subquery[],\n\t) {}\n\n\tprivate authToken?: NeonAuthToken;\n\tsetToken(token: NeonAuthToken) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\tset(\n\t\tvalues: PgUpdateSetSource<TTable>,\n\t): PgUpdateWithout<PgUpdateBase<TTable, TQueryResult>, false, 'leftJoin' | 'rightJoin' | 'innerJoin' | 'fullJoin'> {\n\t\treturn new PgUpdateBase<TTable, TQueryResult>(\n\t\t\tthis.table,\n\t\t\tmapUpdateSet(this.table, values),\n\t\t\tthis.session,\n\t\t\tthis.dialect,\n\t\t\tthis.withList,\n\t\t).setToken(this.authToken);\n\t}\n}\n\nexport type PgUpdateWithout<\n\tT extends AnyPgUpdate,\n\tTDynamic extends boolean,\n\tK extends keyof T & string,\n> = TDynamic extends true ? T : Omit<\n\tPgUpdateBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tT['_']['from'],\n\t\tT['_']['selectedFields'],\n\t\tT['_']['returning'],\n\t\tT['_']['nullabilityMap'],\n\t\tT['_']['joins'],\n\t\tTDynamic,\n\t\tT['_']['excludedMethods'] | K\n\t>,\n\tT['_']['excludedMethods'] | K\n>;\n\nexport type PgUpdateWithJoins<\n\tT extends AnyPgUpdate,\n\tTDynamic extends boolean,\n\tTFrom extends PgTable | Subquery | PgViewBase | SQL,\n> = TDynamic extends true ? T : Omit<\n\tPgUpdateBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tTFrom,\n\t\tT['_']['selectedFields'],\n\t\tT['_']['returning'],\n\t\tAppendToNullabilityMap<T['_']['nullabilityMap'], GetSelectTableName<TFrom>, 'inner'>,\n\t\t[...T['_']['joins'], {\n\t\t\tname: GetSelectTableName<TFrom>;\n\t\t\tjoinType: 'inner';\n\t\t\ttable: TFrom;\n\t\t}],\n\t\tTDynamic,\n\t\tExclude<T['_']['excludedMethods'] | 'from', 'leftJoin' | 'rightJoin' | 'innerJoin' | 'fullJoin'>\n\t>,\n\tExclude<T['_']['excludedMethods'] | 'from', 'leftJoin' | 'rightJoin' | 'innerJoin' | 'fullJoin'>\n>;\n\nexport type PgUpdateJoinFn<\n\tT extends AnyPgUpdate,\n\tTDynamic extends boolean,\n\tTJoinType extends JoinType,\n> = <\n\tTJoinedTable extends PgTable | Subquery | PgViewBase | SQL,\n>(\n\ttable: TableLikeHasEmptySelection<TJoinedTable> extends true ? DrizzleTypeError<\n\t\t\t\"Cannot reference a data-modifying statement subquery if it doesn't contain a `returning` clause\"\n\t\t>\n\t\t: TJoinedTable,\n\ton:\n\t\t| (\n\t\t\t(\n\t\t\t\tupdateTable: T['_']['table']['_']['columns'],\n\t\t\t\tfrom: T['_']['from'] extends PgTable ? T['_']['from']['_']['columns']\n\t\t\t\t\t: T['_']['from'] extends Subquery | PgViewBase ? T['_']['from']['_']['selectedFields']\n\t\t\t\t\t: never,\n\t\t\t) => SQL | undefined\n\t\t)\n\t\t| SQL\n\t\t| undefined,\n) => PgUpdateJoin<T, TDynamic, TJoinType, TJoinedTable>;\n\nexport type PgUpdateJoin<\n\tT extends AnyPgUpdate,\n\tTDynamic extends boolean,\n\tTJoinType extends JoinType,\n\tTJoinedTable extends PgTable | Subquery | PgViewBase | SQL,\n> = TDynamic extends true ? T : PgUpdateBase<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['from'],\n\tT['_']['selectedFields'],\n\tT['_']['returning'],\n\tAppendToNullabilityMap<T['_']['nullabilityMap'], GetSelectTableName<TJoinedTable>, TJoinType>,\n\t[...T['_']['joins'], {\n\t\tname: GetSelectTableName<TJoinedTable>;\n\t\tjoinType: TJoinType;\n\t\ttable: TJoinedTable;\n\t}],\n\tTDynamic,\n\tT['_']['excludedMethods']\n>;\n\ntype Join = {\n\tname: string | undefined;\n\tjoinType: JoinType;\n\ttable: PgTable | Subquery | PgViewBase | SQL;\n};\n\ntype AccumulateToResult<\n\tT extends AnyPgUpdate,\n\tTSelectMode extends SelectMode,\n\tTJoins extends Join[],\n\tTSelectedFields extends ColumnsSelection,\n> = TJoins extends [infer TJoin extends Join, ...infer TRest extends Join[]] ? AccumulateToResult<\n\t\tT,\n\t\tTSelectMode extends 'partial' ? TSelectMode : 'multiple',\n\t\tTRest,\n\t\tAppendToResult<\n\t\t\tT['_']['table']['_']['name'],\n\t\t\tTSelectedFields,\n\t\t\tTJoin['name'],\n\t\t\tTJoin['table'] extends Table ? TJoin['table']['_']['columns']\n\t\t\t\t: TJoin['table'] extends Subquery ? Assume<TJoin['table']['_']['selectedFields'], SelectedFields>\n\t\t\t\t: never,\n\t\t\tTSelectMode extends 'partial' ? TSelectMode : 'multiple'\n\t\t>\n\t>\n\t: TSelectedFields;\n\nexport type PgUpdateReturningAll<T extends AnyPgUpdate, TDynamic extends boolean> = PgUpdateWithout<\n\tPgUpdateBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tT['_']['from'],\n\t\tEqual<T['_']['joins'], []> extends true ? T['_']['table']['_']['columns'] : Simplify<\n\t\t\t& Record<T['_']['table']['_']['name'], T['_']['table']['_']['columns']>\n\t\t\t& {\n\t\t\t\t[K in keyof T['_']['joins'] as T['_']['joins'][K]['table']['_']['name']]:\n\t\t\t\t\tT['_']['joins'][K]['table']['_']['columns'];\n\t\t\t}\n\t\t>,\n\t\tSelectResult<\n\t\t\tAccumulateToResult<\n\t\t\t\tT,\n\t\t\t\t'single',\n\t\t\t\tT['_']['joins'],\n\t\t\t\tGetSelectTableSelection<T['_']['table']>\n\t\t\t>,\n\t\t\t'partial',\n\t\t\tT['_']['nullabilityMap']\n\t\t>,\n\t\tT['_']['nullabilityMap'],\n\t\tT['_']['joins'],\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>,\n\tTDynamic,\n\t'returning'\n>;\n\nexport type PgUpdateReturning<\n\tT extends AnyPgUpdate,\n\tTDynamic extends boolean,\n\tTSelectedFields extends SelectedFields,\n> = PgUpdateWithout<\n\tPgUpdateBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tT['_']['from'],\n\t\tTSelectedFields,\n\t\tSelectResult<\n\t\t\tAccumulateToResult<\n\t\t\t\tT,\n\t\t\t\t'partial',\n\t\t\t\tT['_']['joins'],\n\t\t\t\tTSelectedFields\n\t\t\t>,\n\t\t\t'partial',\n\t\t\tT['_']['nullabilityMap']\n\t\t>,\n\t\tT['_']['nullabilityMap'],\n\t\tT['_']['joins'],\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>,\n\tTDynamic,\n\t'returning'\n>;\n\nexport type PgUpdatePrepare<T extends AnyPgUpdate> = PgPreparedQuery<\n\tPreparedQueryConfig & {\n\t\texecute: T['_']['returning'] extends undefined ? PgQueryResultKind<T['_']['queryResult'], never>\n\t\t\t: T['_']['returning'][];\n\t}\n>;\n\nexport type PgUpdateDynamic<T extends AnyPgUpdate> = PgUpdate<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['from'],\n\tT['_']['returning'],\n\tT['_']['nullabilityMap']\n>;\n\nexport type PgUpdate<\n\tTTable extends PgTable = PgTable,\n\tTQueryResult extends PgQueryResultHKT = PgQueryResultHKT,\n\tTFrom extends PgTable | Subquery | PgViewBase | SQL | undefined = undefined,\n\tTSelectedFields extends ColumnsSelection | undefined = undefined,\n\tTReturning extends Record<string, unknown> | undefined = Record<string, unknown> | undefined,\n\tTNullabilityMap extends Record<string, JoinNullability> = Record<TTable['_']['name'], 'not-null'>,\n\tTJoins extends Join[] = [],\n> = PgUpdateBase<TTable, TQueryResult, TFrom, TSelectedFields, TReturning, TNullabilityMap, TJoins, true, never>;\n\nexport type AnyPgUpdate = PgUpdateBase<any, any, any, any, any, any, any, any, any>;\n\nexport interface PgUpdateBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTFrom extends PgTable | Subquery | PgViewBase | SQL | undefined = undefined,\n\tTSelectedFields extends ColumnsSelection | undefined = undefined,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\tTNullabilityMap extends Record<string, JoinNullability> = Record<TTable['_']['name'], 'not-null'>,\n\tTJoins extends Join[] = [],\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n> extends\n\tTypedQueryBuilder<\n\t\tTSelectedFields,\n\t\tTReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]\n\t>,\n\tQueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>,\n\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\tSQLWrapper\n{\n\treadonly _: {\n\t\treadonly dialect: 'pg';\n\t\treadonly table: TTable;\n\t\treadonly joins: TJoins;\n\t\treadonly nullabilityMap: TNullabilityMap;\n\t\treadonly queryResult: TQueryResult;\n\t\treadonly from: TFrom;\n\t\treadonly selectedFields: TSelectedFields;\n\t\treadonly returning: TReturning;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[];\n\t};\n}\n\nexport class PgUpdateBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTFrom extends PgTable | Subquery | PgViewBase | SQL | undefined = undefined,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTSelectedFields extends ColumnsSelection | undefined = undefined,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTNullabilityMap extends Record<string, JoinNullability> = Record<TTable['_']['name'], 'not-null'>,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTJoins extends Join[] = [],\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTDynamic extends boolean = false,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>\n\timplements\n\t\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\t\tSQLWrapper\n{\n\tstatic override readonly [entityKind]: string = 'PgUpdate';\n\n\tprivate config: PgUpdateConfig;\n\tprivate tableName: string | undefined;\n\tprivate joinsNotNullableMap: Record<string, boolean>;\n\tprotected cacheConfig?: WithCacheConfig;\n\n\tconstructor(\n\t\ttable: TTable,\n\t\tset: UpdateSet,\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t\twithList?: Subquery[],\n\t) {\n\t\tsuper();\n\t\tthis.config = { set, table, withList, joins: [] };\n\t\tthis.tableName = getTableLikeName(table);\n\t\tthis.joinsNotNullableMap = typeof this.tableName === 'string' ? { [this.tableName]: true } : {};\n\t}\n\n\tfrom<TFrom extends PgTable | Subquery | PgViewBase | SQL>(\n\t\tsource: TableLikeHasEmptySelection<TFrom> extends true ? DrizzleTypeError<\n\t\t\t\t\"Cannot reference a data-modifying statement subquery if it doesn't contain a `returning` clause\"\n\t\t\t>\n\t\t\t: TFrom,\n\t): PgUpdateWithJoins<this, TDynamic, TFrom> {\n\t\tconst src = source as TFrom;\n\t\tconst tableName = getTableLikeName(src);\n\t\tif (typeof tableName === 'string') {\n\t\t\tthis.joinsNotNullableMap[tableName] = true;\n\t\t}\n\t\tthis.config.from = src;\n\t\treturn this as any;\n\t}\n\n\tprivate getTableLikeFields(table: PgTable | Subquery | PgViewBase): Record<string, unknown> {\n\t\tif (is(table, PgTable)) {\n\t\t\treturn table[Table.Symbol.Columns];\n\t\t} else if (is(table, Subquery)) {\n\t\t\treturn table._.selectedFields;\n\t\t}\n\t\treturn table[ViewBaseConfig].selectedFields;\n\t}\n\n\tprivate createJoin<TJoinType extends JoinType>(\n\t\tjoinType: TJoinType,\n\t): PgUpdateJoinFn<this, TDynamic, TJoinType> {\n\t\treturn ((\n\t\t\ttable: PgTable | Subquery | PgViewBase | SQL,\n\t\t\ton: ((updateTable: TTable, from: TFrom) => SQL | undefined) | SQL | undefined,\n\t\t) => {\n\t\t\tconst tableName = getTableLikeName(table);\n\n\t\t\tif (typeof tableName === 'string' && this.config.joins.some((join) => join.alias === tableName)) {\n\t\t\t\tthrow new Error(`Alias \"${tableName}\" is already used in this query`);\n\t\t\t}\n\n\t\t\tif (typeof on === 'function') {\n\t\t\t\tconst from = this.config.from && !is(this.config.from, SQL)\n\t\t\t\t\t? this.getTableLikeFields(this.config.from)\n\t\t\t\t\t: undefined;\n\t\t\t\ton = on(\n\t\t\t\t\tnew Proxy(\n\t\t\t\t\t\tthis.config.table[Table.Symbol.Columns],\n\t\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'sql', sqlBehavior: 'sql' }),\n\t\t\t\t\t) as any,\n\t\t\t\t\tfrom && new Proxy(\n\t\t\t\t\t\tfrom,\n\t\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'sql', sqlBehavior: 'sql' }),\n\t\t\t\t\t) as any,\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tthis.config.joins.push({ on, table, joinType, alias: tableName });\n\n\t\t\tif (typeof tableName === 'string') {\n\t\t\t\tswitch (joinType) {\n\t\t\t\t\tcase 'left': {\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = false;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'right': {\n\t\t\t\t\t\tthis.joinsNotNullableMap = Object.fromEntries(\n\t\t\t\t\t\t\tObject.entries(this.joinsNotNullableMap).map(([key]) => [key, false]),\n\t\t\t\t\t\t);\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = true;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'inner': {\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = true;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'full': {\n\t\t\t\t\t\tthis.joinsNotNullableMap = Object.fromEntries(\n\t\t\t\t\t\t\tObject.entries(this.joinsNotNullableMap).map(([key]) => [key, false]),\n\t\t\t\t\t\t);\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = false;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn this as any;\n\t\t}) as any;\n\t}\n\n\tleftJoin = this.createJoin('left');\n\n\trightJoin = this.createJoin('right');\n\n\tinnerJoin = this.createJoin('inner');\n\n\tfullJoin = this.createJoin('full');\n\n\t/**\n\t * Adds a 'where' clause to the query.\n\t *\n\t * Calling this method will update only those rows that fulfill a specified condition.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/update}\n\t *\n\t * @param where the 'where' clause.\n\t *\n\t * @example\n\t * You can use conditional operators and `sql function` to filter the rows to be updated.\n\t *\n\t * ```ts\n\t * // Update all cars with green color\n\t * await db.update(cars).set({ color: 'red' })\n\t *   .where(eq(cars.color, 'green'));\n\t * // or\n\t * await db.update(cars).set({ color: 'red' })\n\t *   .where(sql`${cars.color} = 'green'`)\n\t * ```\n\t *\n\t * You can logically combine conditional operators with `and()` and `or()` operators:\n\t *\n\t * ```ts\n\t * // Update all BMW cars with a green color\n\t * await db.update(cars).set({ color: 'red' })\n\t *   .where(and(eq(cars.color, 'green'), eq(cars.brand, 'BMW')));\n\t *\n\t * // Update all cars with the green or blue color\n\t * await db.update(cars).set({ color: 'red' })\n\t *   .where(or(eq(cars.color, 'green'), eq(cars.color, 'blue')));\n\t * ```\n\t */\n\twhere(where: SQL | undefined): PgUpdateWithout<this, TDynamic, 'where'> {\n\t\tthis.config.where = where;\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `returning` clause to the query.\n\t *\n\t * Calling this method will return the specified fields of the updated rows. If no fields are specified, all fields will be returned.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/update#update-with-returning}\n\t *\n\t * @example\n\t * ```ts\n\t * // Update all cars with the green color and return all fields\n\t * const updatedCars: Car[] = await db.update(cars)\n\t *   .set({ color: 'red' })\n\t *   .where(eq(cars.color, 'green'))\n\t *   .returning();\n\t *\n\t * // Update all cars with the green color and return only their id and brand fields\n\t * const updatedCarsIdsAndBrands: { id: number, brand: string }[] = await db.update(cars)\n\t *   .set({ color: 'red' })\n\t *   .where(eq(cars.color, 'green'))\n\t *   .returning({ id: cars.id, brand: cars.brand });\n\t * ```\n\t */\n\treturning(): PgUpdateReturningAll<this, TDynamic>;\n\treturning<TSelectedFields extends SelectedFields>(\n\t\tfields: TSelectedFields,\n\t): PgUpdateReturning<this, TDynamic, TSelectedFields>;\n\treturning(\n\t\tfields?: SelectedFields,\n\t): PgUpdateWithout<AnyPgUpdate, TDynamic, 'returning'> {\n\t\tif (!fields) {\n\t\t\tfields = Object.assign({}, this.config.table[Table.Symbol.Columns]);\n\n\t\t\tif (this.config.from) {\n\t\t\t\tconst tableName = getTableLikeName(this.config.from);\n\n\t\t\t\tif (typeof tableName === 'string' && this.config.from && !is(this.config.from, SQL)) {\n\t\t\t\t\tconst fromFields = this.getTableLikeFields(this.config.from);\n\t\t\t\t\tfields[tableName] = fromFields as any;\n\t\t\t\t}\n\n\t\t\t\tfor (const join of this.config.joins) {\n\t\t\t\t\tconst tableName = getTableLikeName(join.table);\n\n\t\t\t\t\tif (typeof tableName === 'string' && !is(join.table, SQL)) {\n\t\t\t\t\t\tconst fromFields = this.getTableLikeFields(join.table);\n\t\t\t\t\t\tfields[tableName] = fromFields as any;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tthis.config.returningFields = fields;\n\t\tthis.config.returning = orderSelectedFields<PgColumn>(fields);\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildUpdateQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\t/** @internal */\n\t_prepare(name?: string): PgUpdatePrepare<this> {\n\t\tconst query = this.session.prepareQuery<\n\t\t\tPreparedQueryConfig & { execute: TReturning[] }\n\t\t>(this.dialect.sqlToQuery(this.getSQL()), this.config.returning, name, true, undefined, {\n\t\t\ttype: 'insert',\n\t\t\ttables: extractUsedTable(this.config.table),\n\t\t}, this.cacheConfig);\n\t\tquery.joinsNotNullableMap = this.joinsNotNullableMap;\n\t\treturn query;\n\t}\n\n\tprepare(name: string): PgUpdatePrepare<this> {\n\t\treturn this._prepare(name);\n\t}\n\n\tprivate authToken?: NeonAuthToken;\n\t/** @internal */\n\tsetToken(token?: NeonAuthToken) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\toverride execute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn this._prepare().execute(placeholderValues, this.authToken);\n\t};\n\n\t/** @internal */\n\tgetSelectedFields(): this['_']['selectedFields'] {\n\t\treturn (\n\t\t\tthis.config.returningFields\n\t\t\t\t? new Proxy(\n\t\t\t\t\tthis.config.returningFields,\n\t\t\t\t\tnew SelectionProxyHandler({\n\t\t\t\t\t\talias: getTableName(this.config.table),\n\t\t\t\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\t\t\t\tsqlBehavior: 'error',\n\t\t\t\t\t}),\n\t\t\t\t)\n\t\t\t\t: undefined\n\t\t) as this['_']['selectedFields'];\n\t}\n\n\t$dynamic(): PgUpdateDynamic<this> {\n\t\treturn this as any;\n\t}\n}\n"], "mappings": "AAEA,SAAS,YAAY,UAAU;AAS/B,SAAS,eAAe;AAYxB,SAAS,oBAAoB;AAE7B,SAAS,6BAA6B;AACtC,SAA4C,WAA4B;AACxE,SAAS,gBAAgB;AACzB,SAAS,cAAc,aAAa;AACpC;AAAA,EAIC;AAAA,EACA;AAAA,EAEA;AAAA,OAGM;AACP,SAAS,sBAAsB;AAE/B,SAAS,wBAAwB;AA8B1B,MAAM,gBAA+E;AAAA,EAO3F,YACS,OACA,SACA,SACA,UACP;AAJO;AACA;AACA;AACA;AAAA,EACN;AAAA,EAXH,QAAiB,UAAU,IAAY;AAAA,EAa/B;AAAA,EACR,SAAS,OAAsB;AAC9B,SAAK,YAAY;AACjB,WAAO;AAAA,EACR;AAAA,EAEA,IACC,QACkH;AAClH,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL,aAAa,KAAK,OAAO,MAAM;AAAA,MAC/B,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACN,EAAE,SAAS,KAAK,SAAS;AAAA,EAC1B;AACD;AA6OO,MAAM,qBAeH,aAIV;AAAA,EAQC,YACC,OACA,KACQ,SACA,SACR,UACC;AACD,UAAM;AAJE;AACA;AAIR,SAAK,SAAS,EAAE,KAAK,OAAO,UAAU,OAAO,CAAC,EAAE;AAChD,SAAK,YAAY,iBAAiB,KAAK;AACvC,SAAK,sBAAsB,OAAO,KAAK,cAAc,WAAW,EAAE,CAAC,KAAK,SAAS,GAAG,KAAK,IAAI,CAAC;AAAA,EAC/F;AAAA,EAlBA,QAA0B,UAAU,IAAY;AAAA,EAExC;AAAA,EACA;AAAA,EACA;AAAA,EACE;AAAA,EAeV,KACC,QAI2C;AAC3C,UAAM,MAAM;AACZ,UAAM,YAAY,iBAAiB,GAAG;AACtC,QAAI,OAAO,cAAc,UAAU;AAClC,WAAK,oBAAoB,SAAS,IAAI;AAAA,IACvC;AACA,SAAK,OAAO,OAAO;AACnB,WAAO;AAAA,EACR;AAAA,EAEQ,mBAAmB,OAAiE;AAC3F,QAAI,GAAG,OAAO,OAAO,GAAG;AACvB,aAAO,MAAM,MAAM,OAAO,OAAO;AAAA,IAClC,WAAW,GAAG,OAAO,QAAQ,GAAG;AAC/B,aAAO,MAAM,EAAE;AAAA,IAChB;AACA,WAAO,MAAM,cAAc,EAAE;AAAA,EAC9B;AAAA,EAEQ,WACP,UAC4C;AAC5C,WAAQ,CACP,OACA,OACI;AACJ,YAAM,YAAY,iBAAiB,KAAK;AAExC,UAAI,OAAO,cAAc,YAAY,KAAK,OAAO,MAAM,KAAK,CAAC,SAAS,KAAK,UAAU,SAAS,GAAG;AAChG,cAAM,IAAI,MAAM,UAAU,SAAS,iCAAiC;AAAA,MACrE;AAEA,UAAI,OAAO,OAAO,YAAY;AAC7B,cAAM,OAAO,KAAK,OAAO,QAAQ,CAAC,GAAG,KAAK,OAAO,MAAM,GAAG,IACvD,KAAK,mBAAmB,KAAK,OAAO,IAAI,IACxC;AACH,aAAK;AAAA,UACJ,IAAI;AAAA,YACH,KAAK,OAAO,MAAM,MAAM,OAAO,OAAO;AAAA,YACtC,IAAI,sBAAsB,EAAE,oBAAoB,OAAO,aAAa,MAAM,CAAC;AAAA,UAC5E;AAAA,UACA,QAAQ,IAAI;AAAA,YACX;AAAA,YACA,IAAI,sBAAsB,EAAE,oBAAoB,OAAO,aAAa,MAAM,CAAC;AAAA,UAC5E;AAAA,QACD;AAAA,MACD;AAEA,WAAK,OAAO,MAAM,KAAK,EAAE,IAAI,OAAO,UAAU,OAAO,UAAU,CAAC;AAEhE,UAAI,OAAO,cAAc,UAAU;AAClC,gBAAQ,UAAU;AAAA,UACjB,KAAK,QAAQ;AACZ,iBAAK,oBAAoB,SAAS,IAAI;AACtC;AAAA,UACD;AAAA,UACA,KAAK,SAAS;AACb,iBAAK,sBAAsB,OAAO;AAAA,cACjC,OAAO,QAAQ,KAAK,mBAAmB,EAAE,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,KAAK,CAAC;AAAA,YACrE;AACA,iBAAK,oBAAoB,SAAS,IAAI;AACtC;AAAA,UACD;AAAA,UACA,KAAK,SAAS;AACb,iBAAK,oBAAoB,SAAS,IAAI;AACtC;AAAA,UACD;AAAA,UACA,KAAK,QAAQ;AACZ,iBAAK,sBAAsB,OAAO;AAAA,cACjC,OAAO,QAAQ,KAAK,mBAAmB,EAAE,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,KAAK,CAAC;AAAA,YACrE;AACA,iBAAK,oBAAoB,SAAS,IAAI;AACtC;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAAA,EACD;AAAA,EAEA,WAAW,KAAK,WAAW,MAAM;AAAA,EAEjC,YAAY,KAAK,WAAW,OAAO;AAAA,EAEnC,YAAY,KAAK,WAAW,OAAO;AAAA,EAEnC,WAAW,KAAK,WAAW,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmCjC,MAAM,OAAkE;AACvE,SAAK,OAAO,QAAQ;AACpB,WAAO;AAAA,EACR;AAAA,EA4BA,UACC,QACsD;AACtD,QAAI,CAAC,QAAQ;AACZ,eAAS,OAAO,OAAO,CAAC,GAAG,KAAK,OAAO,MAAM,MAAM,OAAO,OAAO,CAAC;AAElE,UAAI,KAAK,OAAO,MAAM;AACrB,cAAM,YAAY,iBAAiB,KAAK,OAAO,IAAI;AAEnD,YAAI,OAAO,cAAc,YAAY,KAAK,OAAO,QAAQ,CAAC,GAAG,KAAK,OAAO,MAAM,GAAG,GAAG;AACpF,gBAAM,aAAa,KAAK,mBAAmB,KAAK,OAAO,IAAI;AAC3D,iBAAO,SAAS,IAAI;AAAA,QACrB;AAEA,mBAAW,QAAQ,KAAK,OAAO,OAAO;AACrC,gBAAMA,aAAY,iBAAiB,KAAK,KAAK;AAE7C,cAAI,OAAOA,eAAc,YAAY,CAAC,GAAG,KAAK,OAAO,GAAG,GAAG;AAC1D,kBAAM,aAAa,KAAK,mBAAmB,KAAK,KAAK;AACrD,mBAAOA,UAAS,IAAI;AAAA,UACrB;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,SAAK,OAAO,kBAAkB;AAC9B,SAAK,OAAO,YAAY,oBAA8B,MAAM;AAC5D,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,iBAAiB,KAAK,MAAM;AAAA,EACjD;AAAA,EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAS,MAAsC;AAC9C,UAAM,QAAQ,KAAK,QAAQ,aAEzB,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC,GAAG,KAAK,OAAO,WAAW,MAAM,MAAM,QAAW;AAAA,MACvF,MAAM;AAAA,MACN,QAAQ,iBAAiB,KAAK,OAAO,KAAK;AAAA,IAC3C,GAAG,KAAK,WAAW;AACnB,UAAM,sBAAsB,KAAK;AACjC,WAAO;AAAA,EACR;AAAA,EAEA,QAAQ,MAAqC;AAC5C,WAAO,KAAK,SAAS,IAAI;AAAA,EAC1B;AAAA,EAEQ;AAAA;AAAA,EAER,SAAS,OAAuB;AAC/B,SAAK,YAAY;AACjB,WAAO;AAAA,EACR;AAAA,EAES,UAAkD,CAAC,sBAAsB;AACjF,WAAO,KAAK,SAAS,EAAE,QAAQ,mBAAmB,KAAK,SAAS;AAAA,EACjE;AAAA;AAAA,EAGA,oBAAiD;AAChD,WACC,KAAK,OAAO,kBACT,IAAI;AAAA,MACL,KAAK,OAAO;AAAA,MACZ,IAAI,sBAAsB;AAAA,QACzB,OAAO,aAAa,KAAK,OAAO,KAAK;AAAA,QACrC,oBAAoB;AAAA,QACpB,aAAa;AAAA,MACd,CAAC;AAAA,IACF,IACE;AAAA,EAEL;AAAA,EAEA,WAAkC;AACjC,WAAO;AAAA,EACR;AACD;", "names": ["tableName"]}
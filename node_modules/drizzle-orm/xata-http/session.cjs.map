{"version": 3, "sources": ["../../src/xata-http/session.ts"], "sourcesContent": ["import type { SQLPluginResult, SQLQueryResult } from '@xata.io/client';\nimport type { Cache } from '~/cache/core/index.ts';\nimport { NoopCache } from '~/cache/core/index.ts';\nimport type { WithCacheConfig } from '~/cache/core/types.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { NoopLogger } from '~/logger.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport { PgTransaction } from '~/pg-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/pg-core/query-builders/select.types.ts';\nimport type { PgQueryResultHKT, PgTransactionConfig, PreparedQueryConfig } from '~/pg-core/session.ts';\nimport { PgPreparedQuery, PgSession } from '~/pg-core/session.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { fillPlaceholders, type Query } from '~/sql/sql.ts';\nimport { mapResultRow } from '~/utils.ts';\n\nexport type XataHttpClient = {\n\tsql: SQLPluginResult;\n};\n\nexport interface QueryResults<ArrayMode extends 'json' | 'array'> {\n\trowCount: number;\n\trows: ArrayMode extends 'array' ? any[][] : Record<string, any>[];\n\trowAsArray: ArrayMode extends 'array' ? true : false;\n}\n\nexport class XataHttpPreparedQuery<T extends PreparedQueryConfig> extends PgPreparedQuery<T> {\n\tstatic override readonly [entityKind]: string = 'XataHttpPreparedQuery';\n\n\tconstructor(\n\t\tprivate client: XataHttpClient,\n\t\tquery: Query,\n\t\tprivate logger: Logger,\n\t\tcache: Cache,\n\t\tqueryMetadata: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t} | undefined,\n\t\tcacheConfig: WithCacheConfig | undefined,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\tprivate _isResponseInArrayMode: boolean,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => T['execute'],\n\t) {\n\t\tsuper(query, cache, queryMetadata, cacheConfig);\n\t}\n\n\tasync execute(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['execute']> {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues);\n\n\t\tthis.logger.logQuery(this.query.sql, params);\n\n\t\tconst { fields, client, query, customResultMapper, joinsNotNullableMap } = this;\n\n\t\tif (!fields && !customResultMapper) {\n\t\t\treturn this.queryWithCache(query.sql, params, async () => {\n\t\t\t\treturn await client.sql<Record<string, any>>({ statement: query.sql, params });\n\t\t\t});\n\t\t}\n\n\t\tconst { rows, warning } = await this.queryWithCache(query.sql, params, async () => {\n\t\t\treturn await client.sql({ statement: query.sql, params, responseType: 'array' });\n\t\t});\n\n\t\tif (warning) console.warn(warning);\n\n\t\treturn customResultMapper\n\t\t\t? customResultMapper(rows as unknown[][])\n\t\t\t: rows.map((row) => mapResultRow<T['execute']>(fields!, row as unknown[], joinsNotNullableMap));\n\t}\n\n\tall(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['all']> {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues);\n\t\tthis.logger.logQuery(this.query.sql, params);\n\t\treturn this.queryWithCache(this.query.sql, params, async () => {\n\t\t\treturn this.client.sql({ statement: this.query.sql, params, responseType: 'array' });\n\t\t}).then((result) => result.rows);\n\t}\n\n\tvalues(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['values']> {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues);\n\t\tthis.logger.logQuery(this.query.sql, params);\n\t\treturn this.queryWithCache(this.query.sql, params, async () => {\n\t\t\treturn this.client.sql({ statement: this.query.sql, params });\n\t\t}).then((result) => result.records);\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode() {\n\t\treturn this._isResponseInArrayMode;\n\t}\n}\n\nexport interface XataHttpSessionOptions {\n\tlogger?: Logger;\n\tcache?: Cache;\n}\n\nexport class XataHttpSession<TFullSchema extends Record<string, unknown>, TSchema extends TablesRelationalConfig>\n\textends PgSession<\n\t\tXataHttpQueryResultHKT,\n\t\tTFullSchema,\n\t\tTSchema\n\t>\n{\n\tstatic override readonly [entityKind]: string = 'XataHttpSession';\n\n\tprivate logger: Logger;\n\tprivate cache: Cache;\n\n\tconstructor(\n\t\tprivate client: XataHttpClient,\n\t\tdialect: PgDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\tprivate options: XataHttpSessionOptions = {},\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t\tthis.cache = options.cache ?? new NoopCache();\n\t}\n\n\tprepareQuery<T extends PreparedQueryConfig = PreparedQueryConfig>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][]) => T['execute'],\n\t\tqueryMetadata?: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t},\n\t\tcacheConfig?: WithCacheConfig,\n\t): PgPreparedQuery<T> {\n\t\treturn new XataHttpPreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery,\n\t\t\tthis.logger,\n\t\t\tthis.cache,\n\t\t\tqueryMetadata,\n\t\t\tcacheConfig,\n\t\t\tfields,\n\t\t\tisResponseInArrayMode,\n\t\t\tcustomResultMapper,\n\t\t);\n\t}\n\n\tasync query(query: string, params: unknown[]): Promise<QueryResults<'array'>> {\n\t\tthis.logger.logQuery(query, params);\n\t\tconst result = await this.client.sql({ statement: query, params, responseType: 'array' });\n\n\t\treturn {\n\t\t\trowCount: result.rows.length,\n\t\t\trows: result.rows,\n\t\t\trowAsArray: true,\n\t\t};\n\t}\n\n\tasync queryObjects(query: string, params: unknown[]): Promise<QueryResults<'json'>> {\n\t\tconst result = await this.client.sql<Record<string, any>>({ statement: query, params });\n\n\t\treturn {\n\t\t\trowCount: result.records.length,\n\t\t\trows: result.records,\n\t\t\trowAsArray: false,\n\t\t};\n\t}\n\n\toverride async transaction<T>(\n\t\t_transaction: (tx: XataTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\t\t_config: PgTransactionConfig = {},\n\t): Promise<T> {\n\t\tthrow new Error('No transactions support in Xata Http driver');\n\t}\n}\n\nexport class XataTransaction<TFullSchema extends Record<string, unknown>, TSchema extends TablesRelationalConfig>\n\textends PgTransaction<\n\t\tXataHttpQueryResultHKT,\n\t\tTFullSchema,\n\t\tTSchema\n\t>\n{\n\tstatic override readonly [entityKind]: string = 'XataHttpTransaction';\n\n\toverride async transaction<T>(_transaction: (tx: XataTransaction<TFullSchema, TSchema>) => Promise<T>): Promise<T> {\n\t\tthrow new Error('No transactions support in Xata Http driver');\n\t}\n}\n\nexport interface XataHttpQueryResultHKT extends PgQueryResultHKT {\n\ttype: SQLQueryResult<this['row']>;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,kBAA0B;AAE1B,oBAA2B;AAE3B,oBAA2B;AAE3B,qBAA8B;AAG9B,qBAA2C;AAE3C,iBAA6C;AAC7C,mBAA6B;AAYtB,MAAM,8BAA6D,+BAAmB;AAAA,EAG5F,YACS,QACR,OACQ,QACR,OACA,eAIA,aACQ,QACA,wBACA,oBACP;AACD,UAAM,OAAO,OAAO,eAAe,WAAW;AAbtC;AAEA;AAOA;AACA;AACA;AAAA,EAGT;AAAA,EAjBA,QAA0B,wBAAU,IAAY;AAAA,EAmBhD,MAAM,QAAQ,oBAAyD,CAAC,GAA0B;AACjG,UAAM,aAAS,6BAAiB,KAAK,MAAM,QAAQ,iBAAiB;AAEpE,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAE3C,UAAM,EAAE,QAAQ,QAAQ,OAAO,oBAAoB,oBAAoB,IAAI;AAE3E,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,aAAO,KAAK,eAAe,MAAM,KAAK,QAAQ,YAAY;AACzD,eAAO,MAAM,OAAO,IAAyB,EAAE,WAAW,MAAM,KAAK,OAAO,CAAC;AAAA,MAC9E,CAAC;AAAA,IACF;AAEA,UAAM,EAAE,MAAM,QAAQ,IAAI,MAAM,KAAK,eAAe,MAAM,KAAK,QAAQ,YAAY;AAClF,aAAO,MAAM,OAAO,IAAI,EAAE,WAAW,MAAM,KAAK,QAAQ,cAAc,QAAQ,CAAC;AAAA,IAChF,CAAC;AAED,QAAI,QAAS,SAAQ,KAAK,OAAO;AAEjC,WAAO,qBACJ,mBAAmB,IAAmB,IACtC,KAAK,IAAI,CAAC,YAAQ,2BAA2B,QAAS,KAAkB,mBAAmB,CAAC;AAAA,EAChG;AAAA,EAEA,IAAI,oBAAyD,CAAC,GAAsB;AACnF,UAAM,aAAS,6BAAiB,KAAK,MAAM,QAAQ,iBAAiB;AACpE,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAC3C,WAAO,KAAK,eAAe,KAAK,MAAM,KAAK,QAAQ,YAAY;AAC9D,aAAO,KAAK,OAAO,IAAI,EAAE,WAAW,KAAK,MAAM,KAAK,QAAQ,cAAc,QAAQ,CAAC;AAAA,IACpF,CAAC,EAAE,KAAK,CAAC,WAAW,OAAO,IAAI;AAAA,EAChC;AAAA,EAEA,OAAO,oBAAyD,CAAC,GAAyB;AACzF,UAAM,aAAS,6BAAiB,KAAK,MAAM,QAAQ,iBAAiB;AACpE,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAC3C,WAAO,KAAK,eAAe,KAAK,MAAM,KAAK,QAAQ,YAAY;AAC9D,aAAO,KAAK,OAAO,IAAI,EAAE,WAAW,KAAK,MAAM,KAAK,OAAO,CAAC;AAAA,IAC7D,CAAC,EAAE,KAAK,CAAC,WAAW,OAAO,OAAO;AAAA,EACnC;AAAA;AAAA,EAGA,wBAAwB;AACvB,WAAO,KAAK;AAAA,EACb;AACD;AAOO,MAAM,wBACJ,yBAKT;AAAA,EAMC,YACS,QACR,SACQ,QACA,UAAkC,CAAC,GAC1C;AACD,UAAM,OAAO;AALL;AAEA;AACA;AAGR,SAAK,SAAS,QAAQ,UAAU,IAAI,yBAAW;AAC/C,SAAK,QAAQ,QAAQ,SAAS,IAAI,sBAAU;AAAA,EAC7C;AAAA,EAdA,QAA0B,wBAAU,IAAY;AAAA,EAExC;AAAA,EACA;AAAA,EAaR,aACC,OACA,QACA,MACA,uBACA,oBACA,eAIA,aACqB;AACrB,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAM,MAAM,OAAe,QAAmD;AAC7E,SAAK,OAAO,SAAS,OAAO,MAAM;AAClC,UAAM,SAAS,MAAM,KAAK,OAAO,IAAI,EAAE,WAAW,OAAO,QAAQ,cAAc,QAAQ,CAAC;AAExF,WAAO;AAAA,MACN,UAAU,OAAO,KAAK;AAAA,MACtB,MAAM,OAAO;AAAA,MACb,YAAY;AAAA,IACb;AAAA,EACD;AAAA,EAEA,MAAM,aAAa,OAAe,QAAkD;AACnF,UAAM,SAAS,MAAM,KAAK,OAAO,IAAyB,EAAE,WAAW,OAAO,OAAO,CAAC;AAEtF,WAAO;AAAA,MACN,UAAU,OAAO,QAAQ;AAAA,MACzB,MAAM,OAAO;AAAA,MACb,YAAY;AAAA,IACb;AAAA,EACD;AAAA,EAEA,MAAe,YACd,cAEA,UAA+B,CAAC,GACnB;AACb,UAAM,IAAI,MAAM,6CAA6C;AAAA,EAC9D;AACD;AAEO,MAAM,wBACJ,6BAKT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,MAAe,YAAe,cAAqF;AAClH,UAAM,IAAI,MAAM,6CAA6C;AAAA,EAC9D;AACD;", "names": []}
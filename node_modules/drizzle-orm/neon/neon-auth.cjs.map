{"version": 3, "sources": ["../../src/neon/neon-auth.ts"], "sourcesContent": ["import { jsonb, pgSchema, text, timestamp } from '~/pg-core/index.ts';\n\nconst neonAuthSchema = pgSchema('neon_auth');\n\n/**\n * Table schema of the `users_sync` table used by <PERSON><PERSON> Auth.\n * This table automatically synchronizes and stores user data from external authentication providers.\n *\n * @schema neon_auth\n * @table users_sync\n */\nexport const usersSync = neonAuthSchema.table('users_sync', {\n\trawJson: jsonb('raw_json').notNull(),\n\tid: text().primaryKey().notNull(),\n\tname: text(),\n\temail: text(),\n\tcreatedAt: timestamp('created_at', { withTimezone: true, mode: 'string' }),\n\tdeletedAt: timestamp('deleted_at', { withTimezone: true, mode: 'string' }),\n\tupdatedAt: timestamp('updated_at', { withTimezone: true, mode: 'string' }),\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAAiD;AAEjD,MAAM,qBAAiB,yBAAS,WAAW;AASpC,MAAM,YAAY,eAAe,MAAM,cAAc;AAAA,EAC3D,aAAS,sBAAM,UAAU,EAAE,QAAQ;AAAA,EACnC,QAAI,qBAAK,EAAE,WAAW,EAAE,QAAQ;AAAA,EAChC,UAAM,qBAAK;AAAA,EACX,WAAO,qBAAK;AAAA,EACZ,eAAW,0BAAU,cAAc,EAAE,cAAc,MAAM,MAAM,SAAS,CAAC;AAAA,EACzE,eAAW,0BAAU,cAAc,EAAE,cAAc,MAAM,MAAM,SAAS,CAAC;AAAA,EACzE,eAAW,0BAAU,cAAc,EAAE,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1E,CAAC;", "names": []}
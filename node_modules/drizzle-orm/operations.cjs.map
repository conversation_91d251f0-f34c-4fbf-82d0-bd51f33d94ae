{"version": 3, "sources": ["../src/operations.ts"], "sourcesContent": ["import type { AnyColumn, Column } from './column.ts';\nimport type { SQL } from './sql/sql.ts';\nimport type { Table } from './table.ts';\n\nexport type RequiredKeyOnly<TK<PERSON> extends string, T extends Column> = T extends AnyColumn<{\n\tnotNull: true;\n\thasDefault: false;\n}> ? TKey\n\t: never;\n\nexport type OptionalKeyOnly<TKey extends string, T extends Column, OverrideT extends boolean | undefined = false> =\n\tT<PERSON><PERSON> extends RequiredKeyOnly<TKey, T> ? never : T extends {\n\t\t_: {\n\t\t\tgenerated: undefined;\n\t\t};\n\t} ? (T extends {\n\t\t\t_: {\n\t\t\t\tidentity: undefined;\n\t\t\t};\n\t\t} ? TKey\n\t\t\t: T['_']['identity'] extends 'always' ? OverrideT extends true ? TKey : never\n\t\t\t: TKey)\n\t: never;\n\n// TODO: SQL -> SQLWrapper\nexport type SelectedFieldsFlat<TColumn extends Column> = Record<\n\tstring,\n\tTColumn | SQL | SQL.Aliased\n>;\n\nexport type SelectedFieldsFlatFull<TColumn extends Column> = Record<\n\tstring,\n\tTColumn | SQL | SQL.Aliased\n>;\n\nexport type SelectedFields<TColumn extends Column, TTable extends Table> = Record<\n\tstring,\n\tSelectedFieldsFlat<TColumn>[string] | TTable | SelectedFieldsFlat<TColumn>\n>;\n\nexport type SelectedFieldsOrdered<TColumn extends Column> = {\n\tpath: string[];\n\tfield: TColumn | SQL | SQL.Aliased;\n}[];\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}
{"name": "249ayman_interview-prep", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "db:push": "drizzle-kit push", "db:seed": "drizzle-kit seed", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "start": "next start", "lint": "next lint"}, "dependencies": {"@neondatabase/serverless": "^1.0.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "better-auth": "^1.3.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.2.1", "drizzle-orm": "^0.44.4", "lucide-react": "^0.539.0", "next": "15.4.6", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.4", "eslint": "^9", "eslint-config-next": "15.4.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4", "tsx": "^4.20.4", "tw-animate-css": "^1.3.7", "typescript": "^5"}}